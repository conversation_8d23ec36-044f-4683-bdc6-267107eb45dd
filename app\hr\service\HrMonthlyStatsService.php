<?php
declare(strict_types=1);

namespace app\hr\service;

use app\common\utils\DateCalculator;
use app\hr\model\HrLeave;
use app\hr\model\HrOuting;
use app\hr\model\HrBusinessTripItinerary;
use app\system\model\AdminModel;

/**
 * HR月度统计服务类
 * 负责处理请假、外出、出差的月度统计功能
 */
class HrMonthlyStatsService
{
    /**
     * 获取员工月度综合统计
     * 
     * @param int $adminId 员工ID
     * @param string $yearMonth 年月，格式：2025-01
     * @return array 月度统计数据
     */
    public function getEmployeeMonthlyStats(int $adminId, string $yearMonth): array
    {
        $startDate = $yearMonth . '-01 00:00:00';
        $endDate = date('Y-m-t 23:59:59', strtotime($startDate));
        
        // 请假统计
        $leaveStats = $this->getLeaveStats($adminId, $startDate, $endDate);
        
        // 外出统计
        $outingHours = HrOuting::where('submitter_id', $adminId)
            ->where('approval_status', 2) // 已通过
            ->whereBetweenTime('start_time', $startDate, $endDate)
            ->sum('duration');
            
        // 出差统计 - 需要从明细表中统计
        $businessTripHours = $this->getBusinessTripHours($adminId, $startDate, $endDate);
            
        $totalHours = $leaveStats['total_hours'] + $outingHours + $businessTripHours;
        
        // 获取工作时间配置
        $workTimeService = new HrWorkTimeService();
        $dailyWorkHours = $workTimeService->getDailyWorkHours();
        $displayFormat = DateCalculator::convertHoursToDaysAndHours($totalHours, $dailyWorkHours);
        
        return [
            'year_month' => $yearMonth,
            'employee_id' => $adminId,
            'leave_stats' => $leaveStats,
            'outing_hours' => (float)$outingHours,
            'business_trip_hours' => (float)$businessTripHours,
            'total_hours' => $totalHours,
            'daily_work_hours' => $dailyWorkHours,
            'display_format' => $displayFormat['display'],
            'days_breakdown' => $displayFormat
        ];
    }
    
    /**
     * 获取请假详细统计
     * 
     * @param int $adminId 员工ID
     * @param string $startDate 开始日期
     * @param string $endDate 结束日期
     * @return array 请假统计数据
     */
    private function getLeaveStats(int $adminId, string $startDate, string $endDate): array
    {
        $leaveRecords = HrLeave::where('submitter_id', $adminId)
            ->where('approval_status', 2) // 已通过
            ->whereBetweenTime('start_time', $startDate, $endDate)
            ->field('leave_type,duration')
            ->select();
            
        $stats = [
            'total_hours' => 0.0,
            'by_type' => [
                1 => ['name' => '年假', 'hours' => 0.0],
                2 => ['name' => '事假', 'hours' => 0.0],
                3 => ['name' => '病假', 'hours' => 0.0],
                4 => ['name' => '婚假', 'hours' => 0.0],
                5 => ['name' => '产假', 'hours' => 0.0],
                6 => ['name' => '丧假', 'hours' => 0.0],
                7 => ['name' => '其他', 'hours' => 0.0],
            ]
        ];
        
        foreach ($leaveRecords as $record) {
            $stats['total_hours'] += $record->duration;
            if (isset($stats['by_type'][$record->leave_type])) {
                $stats['by_type'][$record->leave_type]['hours'] += $record->duration;
            }
        }
        
        return $stats;
    }

    /**
     * 获取出差时长统计
     * 从出差明细表中统计指定时间范围内的出差时长
     *
     * @param int $adminId 员工ID，0表示全部员工
     * @param string $startDate 开始日期
     * @param string $endDate 结束日期
     * @return float 出差总时长
     */
    private function getBusinessTripHours(int $adminId, string $startDate, string $endDate): float
    {
        // 构建关联查询条件
        $tripConditions = ['approval_status' => 2]; // 已通过的出差申请
        if ($adminId > 0) {
            $tripConditions['submitter_id'] = $adminId;
        }

        // 使用模型关联查询，一次性传入所有条件
        $query = HrBusinessTripItinerary::whereBetweenTime('start_time', $startDate, $endDate)
            ->hasWhere('trip', $tripConditions);

        // 统计总时长
        return (float)$query->sum('duration');
    }
    
    /**
     * 获取部门月度统计
     * 
     * @param int $deptId 部门ID
     * @param string $yearMonth 年月，格式：2025-01
     * @return array 部门统计数据
     */
    public function getDepartmentMonthlyStats(int $deptId, string $yearMonth): array
    {
        // 获取部门所有员工
        $employees = AdminModel::where('dept_id', $deptId)
            ->where('status', 1)
            ->field('id,real_name')
            ->select();
            
        $departmentStats = [
            'dept_id' => $deptId,
            'year_month' => $yearMonth,
            'employee_count' => count($employees),
            'total_hours' => 0.0,
            'employees' => []
        ];
        
        foreach ($employees as $employee) {
            $employeeStats = $this->getEmployeeMonthlyStats($employee->id, $yearMonth);
            $employeeStats['employee_name'] = $employee->real_name;
            
            $departmentStats['employees'][] = $employeeStats;
            $departmentStats['total_hours'] += $employeeStats['total_hours'];
        }
        
        // 部门总计的显示格式
        $workTimeService = new HrWorkTimeService();
        $dailyWorkHours = $workTimeService->getDailyWorkHours();
        $displayFormat = DateCalculator::convertHoursToDaysAndHours($departmentStats['total_hours'], $dailyWorkHours);
        $departmentStats['display_format'] = $displayFormat['display'];
        $departmentStats['days_breakdown'] = $displayFormat;
        
        return $departmentStats;
    }
    
    /**
     * 获取员工年度统计
     * 
     * @param int $adminId 员工ID
     * @param int $year 年份
     * @return array 年度统计数据
     */
    public function getEmployeeYearlyStats(int $adminId, int $year): array
    {
        $yearlyStats = [
            'year' => $year,
            'employee_id' => $adminId,
            'monthly_stats' => [],
            'total_hours' => 0.0
        ];
        
        // 获取12个月的统计数据
        for ($month = 1; $month <= 12; $month++) {
            $yearMonth = sprintf('%d-%02d', $year, $month);
            $monthlyStats = $this->getEmployeeMonthlyStats($adminId, $yearMonth);
            
            $yearlyStats['monthly_stats'][] = $monthlyStats;
            $yearlyStats['total_hours'] += $monthlyStats['total_hours'];
        }
        
        // 年度总计的显示格式
        $workTimeService = new HrWorkTimeService();
        $dailyWorkHours = $workTimeService->getDailyWorkHours();
        $displayFormat = DateCalculator::convertHoursToDaysAndHours($yearlyStats['total_hours'], $dailyWorkHours);
        $yearlyStats['display_format'] = $displayFormat['display'];
        $yearlyStats['days_breakdown'] = $displayFormat;
        
        return $yearlyStats;
    }
    
    /**
     * 获取统计数据概览
     * 
     * @param int $adminId 员工ID，0表示全部员工
     * @param string $yearMonth 年月，格式：2025-01
     * @return array 统计概览数据
     */
    public function getStatsOverview(int $adminId = 0, string $yearMonth = ''): array
    {
        if (empty($yearMonth)) {
            $yearMonth = date('Y-m');
        }
        
        $startDate = $yearMonth . '-01 00:00:00';
        $endDate = date('Y-m-t 23:59:59', strtotime($startDate));
        
        $whereCondition = [];
        if ($adminId > 0) {
            $whereCondition['submitter_id'] = $adminId;
        }
        
        // 请假统计
        $leaveQuery = HrLeave::where('approval_status', 2)
            ->whereBetweenTime('start_time', $startDate, $endDate);
        if ($adminId > 0) {
            $leaveQuery->where('submitter_id', $adminId);
        }
        $totalLeaveHours = $leaveQuery->sum('duration');
        
        // 外出统计
        $outingQuery = HrOuting::where('approval_status', 2)
            ->whereBetweenTime('start_time', $startDate, $endDate);
        if ($adminId > 0) {
            $outingQuery->where('submitter_id', $adminId);
        }
        $totalOutingHours = $outingQuery->sum('duration');
        
        // 出差统计 - 需要从明细表中统计
        $totalBusinessTripHours = $this->getBusinessTripHours($adminId, $startDate, $endDate);
        
        $totalHours = $totalLeaveHours + $totalOutingHours + $totalBusinessTripHours;
        
        // 获取工作时间配置
        $workTimeService = new HrWorkTimeService();
        $dailyWorkHours = $workTimeService->getDailyWorkHours();
        $displayFormat = DateCalculator::convertHoursToDaysAndHours($totalHours, $dailyWorkHours);
        
        return [
            'year_month' => $yearMonth,
            'scope' => $adminId > 0 ? 'employee' : 'all',
            'employee_id' => $adminId,
            'leave_hours' => (float)$totalLeaveHours,
            'outing_hours' => (float)$totalOutingHours,
            'business_trip_hours' => (float)$totalBusinessTripHours,
            'total_hours' => $totalHours,
            'daily_work_hours' => $dailyWorkHours,
            'display_format' => $displayFormat['display'],
            'days_breakdown' => $displayFormat
        ];
    }
}
