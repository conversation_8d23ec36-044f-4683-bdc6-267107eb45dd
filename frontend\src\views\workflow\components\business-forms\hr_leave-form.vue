<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="60%"
    top="5vh"
    :close-on-click-modal="false"
    :before-close="handleDialogClose"
    destroy-on-close
    class="hr-leave-dialog"
  >
    <div class="dialog-content" v-loading="loading">
      <!-- 主表单 -->
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        label-position="right"
        class="leave-form"
      >
        <!-- 基本信息 -->
        <div class="form-section">
          <div class="section-title">
            <el-icon>
              <User />
            </el-icon>
            基本信息
          </div>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="请假类型" prop="leave_type" required>
                <el-select
                  v-model="formData.leave_type"
                  placeholder="请选择请假类型"
                  style="width: 100%"
                  :disabled="!canEdit"
                >
                  <el-option
                    v-for="item in leaveTypeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="请假时长" prop="duration" required>
                <el-input-number
                  v-model="formData.duration"
                  :min="0.5"
                  :max="2920"
                  :step="0.5"
                  :precision="1"
                  placeholder="请假时长（小时）"
                  style="width: 100%"
                  :disabled="!canEdit"
                />
                <div class="field-tip">按半小时向上取整规则自动计算，也可手动调整</div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 时间信息 -->
        <div class="form-section">
          <div class="section-title">
            <el-icon>
              <Clock />
            </el-icon>
            时间信息
          </div>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="开始时间" prop="start_time" required>
                <el-date-picker
                  v-model="formData.start_time"
                  type="datetime"
                  placeholder="选择开始时间"
                  format="YYYY-MM-DD HH:mm"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  style="width: 100%"
                  :disabled="!canEdit"
                  @change="handleTimeChange"
                />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="结束时间" prop="end_time" required>
                <el-date-picker
                  v-model="formData.end_time"
                  type="datetime"
                  placeholder="选择结束时间"
                  format="YYYY-MM-DD HH:mm"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  style="width: 100%"
                  :disabled="!canEdit"
                  @change="handleTimeChange"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 详细信息 -->
        <div class="form-section">
          <div class="section-title">
            <el-icon>
              <Document />
            </el-icon>
            详细信息
          </div>

          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="请假原因" prop="reason" required>
                <el-input
                  v-model="formData.reason"
                  type="textarea"
                  :rows="4"
                  placeholder="请详细说明请假原因（最多500字符）"
                  maxlength="500"
                  show-word-limit
                  :disabled="!canEdit"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="紧急联系人" prop="emergency_contact">
                <el-input
                  v-model="formData.emergency_contact"
                  placeholder="请输入紧急联系人姓名"
                  :disabled="!canEdit"
                />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="联系电话" prop="emergency_phone">
                <el-input
                  v-model="formData.emergency_phone"
                  placeholder="请输入紧急联系人电话"
                  :disabled="!canEdit"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 附件信息 -->
        <div class="form-section">
          <div class="section-title">
            <el-icon>
              <Paperclip />
            </el-icon>
            附件信息
          </div>

          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="相关附件" prop="attachment">
                <FormUploader
                  v-model="formData.attachment"
                  fileType="image"
                  :limit="5"
                  :multiple="true"
                  :disabled="!canEdit"
                  returnValueMode="string"
                  buttonText="选择附件"
                  tipText="支持上传图片格式，最多5个。可按住Ctrl键选择多个文件"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="备注信息" prop="remark">
                <el-input
                  v-model="formData.remark"
                  type="textarea"
                  :rows="3"
                  placeholder="其他需要说明的信息（可选）"
                  maxlength="200"
                  show-word-limit
                  :disabled="!canEdit"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 审批信息（仅查看时显示） -->
        <div class="form-section" v-if="formData.id && formData.approval_status > 0">
          <div class="section-title">
            <el-icon>
              <Checked />
            </el-icon>
            审批信息
          </div>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="提交时间">
                <el-input :value="formData.submit_time || '未提交'" readonly style="width: 100%" />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="审批时间">
                <el-input
                  :value="formData.approval_time || '未审批'"
                  readonly
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20" v-if="formData.approval_opinion">
            <el-col :span="24">
              <el-form-item label="审批意见">
                <el-input :value="formData.approval_opinion" type="textarea" :rows="3" readonly />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
    </div>

    <!-- 对话框底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel"> 取消</el-button>

        <el-button v-if="canEdit" type="primary" @click="handleSave" :loading="saving">
          保存草稿
        </el-button>

        <el-button v-if="canEdit" type="success" @click="handleSubmit" :loading="submitting">
          提交审批
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import {
    ElMessage,
    ElMessageBox,
    type FormInstance,
    type FormRules
  } from 'element-plus'
  import { User, Clock, Document, Paperclip, Checked } from '@element-plus/icons-vue'
  import HrLeaveApi, { type HrLeaveItem, type HrLeaveFormData } from '@/api/hr/hrLeave'
  import { ApplicationApi } from '@/api/workflow/ApplicationApi'
  import { ApiStatus } from '@/utils/http/status'
  import { FormUploader } from '@/components/custom/FormUploader'

  /**
   * 组件属性定义
   */
  interface Props {
    /** 对话框显示状态 */
    modelValue: boolean
    /** 表单数据ID（编辑时传入） */
    formId?: number | string
    /** 工作流定义ID */
    definitionId?: number | string
  }

  /**
   * 组件事件定义
   */
  interface Emits {
    /** 更新显示状态 */
    (e: 'update:modelValue', value: boolean): void

    /** 保存成功事件 */
    (e: 'save', data: HrLeaveItem): void

    /** 提交成功事件 */
    (e: 'submit', data: { instance_id: number; leave_id: number }): void

    /** 撤回成功事件 */
    (e: 'withdraw'): void

    /** 取消事件 */
    (e: 'cancel'): void

    /** 成功事件（统一） */
    (e: 'success', data: any): void
  }

  // 组件属性和事件
  const props = withDefaults(defineProps<Props>(), {
    modelValue: false,
    definitionId: 0
  })

  const emit = defineEmits<Emits>()

  // 调试信息：监听 definitionId 变化
  watch(
    () => props.definitionId,
    (newVal) => {
      console.log('hr_leave-form 接收到 definitionId:', newVal)
    },
    { immediate: true }
  )

  // ==================== 响应式数据 ====================

  /** 表单引用 */
  const formRef = ref<FormInstance>()

  /** 对话框显示状态 */
  const dialogVisible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
  })

  /** 对话框标题 */
  const dialogTitle = computed(() => {
    if (formData.id) {
      return `请假申请 - ${formData.approval_status_text || '草稿'}`
    }
    return '发起请假申请'
  })

  /** 表单数据 */
  const formData = reactive<HrLeaveFormData & Partial<HrLeaveItem>>({
    leave_type: 1,
    start_time: '',
    end_time: '',
    duration: 0.5,
    reason: '',
    emergency_contact: '',
    emergency_phone: '',
    attachment: [],
    remark: '',
    approval_status: 0  // 添加默认状态，确保发起表单可编辑
  })

  /** 请假类型选项 */
  const leaveTypeOptions = ref([])

  /** 加载状态 */
  const loading = ref(false)
  const saving = ref(false)
  const submitting = ref(false)

  // ==================== 计算属性 ====================

  /** 是否可以编辑 */
  const canEdit = computed(() => {
    // 发起表单（无状态或状态为0）或草稿状态都可以编辑
    return !formData.approval_status || formData.approval_status === 0
  })

  // ==================== 表单验证规则 ====================

  /** 表单验证规则 */
  const formRules: FormRules = {
    leave_type: [{ required: true, message: '请选择请假类型', trigger: 'change' }],
    start_time: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
    end_time: [
      { required: true, message: '请选择结束时间', trigger: 'change' },
      {
        validator: (rule, value, callback) => {
          if (value && formData.start_time && value <= formData.start_time) {
            callback(new Error('结束时间必须晚于开始时间'))
          } else {
            callback()
          }
        },
        trigger: 'change'
      }
    ],
    duration: [
      { required: true, message: '请输入请假时长', trigger: 'blur' },
      {
        type: 'number',
        min: 0.5,
        max: 2920,
        message: '请假时长必须在0.5-2920小时之间',
        trigger: 'blur'
      }
    ],
    reason: [
      { required: true, message: '请输入请假原因', trigger: 'blur' },
      { min: 5, max: 500, message: '请假原因长度在5-500个字符之间', trigger: 'blur' }
    ],
    emergency_phone: [
      {
        pattern: /^1[3-9]\d{9}$/,
        message: '请输入正确的手机号码',
        trigger: 'blur'
      }
    ]
  }

  // ==================== 方法定义 ====================

  /**
   * 获取状态类型（用于标签显示）
   */
  const getStatusType = (status: number): string => {
    const typeMap: Record<number, string> = {
      0: 'info', // 草稿
      1: 'warning', // 审批中
      2: 'success', // 已通过
      3: 'danger', // 已拒绝
      4: 'danger', // 已终止
      5: 'info', // 已撤回
      6: 'danger' // 已作废
    }
    return typeMap[status] || 'default'
  }

  /**
   * 处理时间变化
   */
  const handleTimeChange = () => {
    if (formData.start_time && formData.end_time) {
      const validation = HrLeaveApi.validateLeaveTime(formData.start_time, formData.end_time)
      if (validation.valid) {
        formData.duration = HrLeaveApi.calculateDuration(formData.start_time, formData.end_time)
      }
    }
  }



  /**
   * 显示表单（供FormManager调用）
   */
  const showForm = async (id?: number | string) => {
    console.log('hr_leave-form showForm called with id:', id)

    if (id && id !== '0') {
      await loadFormData(id)
    } else {
      // 重置表单为发起状态
      resetForm()
    }
  }

  /**
   * 重置表单
   */
  const resetForm = () => {
    Object.assign(formData, {
      id: undefined,
      leave_type: 1,
      start_time: '',
      end_time: '',
      duration: 0.5,
      reason: '',
      emergency_contact: '',
      emergency_phone: '',
      attachment: [],
      remark: '',
      approval_status: 0,
      workflow_instance_id: 0
    })

  }

  /**
   * 加载表单数据
   */
  const loadFormData = async (id: number | string) => {
    try {
      loading.value = true
      // 使用通用工作流接口获取详情
      const response = await ApplicationApi.detail(id)

      if (response.code === ApiStatus.success) {
        // 从 response.data.form_data 中获取真正的表单数据
        const actualFormData = response.data.form_data || response.data
        console.log('hr_leave-form loadFormData 获取到的数据:', actualFormData)

        // 合并基础信息和表单数据
        Object.assign(formData, {
          id: response.data.id,
          approval_status: response.data.status || 0,
          approval_status_text: response.data.status_text || '草稿',
          workflow_instance_id: response.data.id || 0,
          ...actualFormData
        })

        // 处理附件数据
        if (formData.attachment) {
          if (typeof formData.attachment === 'string') {
            try {
              formData.attachment = JSON.parse(formData.attachment)
            } catch {
              formData.attachment = []
            }
          }


        }

        console.log('hr_leave-form 表单数据加载完成:', formData)
      }
    } catch (error) {
      console.error('加载表单数据失败:', error)
      // ElMessage.error('加载数据失败')
    } finally {
      loading.value = false
    }
  }

  /**
   * 保存表单数据
   */
  const handleSave = async () => {
    console.log('hr_leave-form.handleSave 被调用')
    try {
      // 表单验证
      const valid = await formRef.value?.validate()
      if (!valid) return

      saving.value = true

      // 准备提交数据
      const submitData: HrLeaveFormData = {
        leave_type: formData.leave_type,
        start_time: formData.start_time,
        end_time: formData.end_time,
        duration: formData.duration,
        reason: formData.reason,
        emergency_contact: formData.emergency_contact,
        emergency_phone: formData.emergency_phone,
        attachment: formData.attachment,
        remark: formData.remark
      }

      // 如果是编辑模式，添加ID
      if (formData.id) {
        submitData.id = formData.id
      }

      // 触发保存事件，让父组件处理API调用
      console.log('hr_leave-form 触发 save 事件，数据:', submitData)
      emit('save', submitData)

      // 注意：saving状态会在父组件处理完成后重置
    } catch (error) {
      console.error('保存失败:', error)
      ElMessage.error('保存失败')
      saving.value = false
    }
  }

  /**
   * 提交审批
   */
  const handleSubmit = async () => {
    try {
      // 表单验证
      const valid = await formRef.value?.validate()
      if (!valid) return

      await ElMessageBox.confirm('确定要提交审批吗？提交后将无法修改。', '确认提交', {
        confirmButtonText: '确定提交',
        cancelButtonText: '取消',
        type: 'warning'
      })

      submitting.value = true

      // 准备提交数据
      const submitData: HrLeaveFormData = {
        leave_type: formData.leave_type,
        start_time: formData.start_time,
        end_time: formData.end_time,
        duration: formData.duration,
        reason: formData.reason,
        emergency_contact: formData.emergency_contact,
        emergency_phone: formData.emergency_phone,
        attachment: formData.attachment,
        remark: formData.remark
      }

      // 如果是编辑模式，添加ID
      if (formData.id) {
        submitData.id = formData.id
      }

      // 触发提交事件，让父组件处理API调用
      emit('submit', submitData)

      // 注意：submitting状态会在父组件处理完成后重置
    } catch (error) {
      if (error !== 'cancel') {
        console.error('提交审批失败:', error)
        ElMessage.error('提交审批失败')
      }
      submitting.value = false
    }
  }

  /**
   * 取消操作
   */
  const handleCancel = () => {
    dialogVisible.value = false
    // 重置标记
    isDataSetBySetFormData.value = false
    emit('cancel')
  }

  /**
   * 对话框关闭处理
   */
  const handleDialogClose = () => {
    dialogVisible.value = false
    // 重置标记
    isDataSetBySetFormData.value = false
    emit('cancel')
  }

  // ==================== 生命周期 ====================

  onMounted(async () => {
    // 加载请假类型选项
    try {
      const response = await HrLeaveApi.getLeaveTypes()
      if (response.code === 1) {
        leaveTypeOptions.value = response.data
      }
    } catch (error) {
      console.error('加载请假类型失败:', error)
    }
  })

  // 标记是否通过setFormData设置了数据
  const isDataSetBySetFormData = ref(false)

  // 监听formId变化
  watch(
    () => props.formId,
    (newId) => {
      console.log(
        'hr_leave-form watch formId 变化:',
        newId,
        '对话框可见:',
        dialogVisible.value,
        '当前表单ID:',
        formData.id,
        '是否通过setFormData设置:',
        isDataSetBySetFormData.value
      )

      // 只有在对话框可见、有新ID、且不是通过setFormData设置数据时才加载
      if (newId && dialogVisible.value && !isDataSetBySetFormData.value) {
        console.log('hr_leave-form watch 触发 loadFormData')
        loadFormData(newId)
      } else {
        console.log('hr_leave-form watch 跳过 loadFormData，原因：', {
          hasNewId: !!newId,
          dialogVisible: dialogVisible.value,
          isDataSetBySetFormData: isDataSetBySetFormData.value
        })
      }
    },
    { immediate: false }
  )

  /**
   * 更新表单数据（供父组件调用）
   */
  const updateFormData = (newData: any) => {
    if (newData) {
      Object.assign(formData, newData)
      console.log('hr_leave-form 表单数据已更新:', formData)
    }
  }

  /**
   * 设置表单数据（供FormManager调用）
   */
  const setFormData = (data: any) => {
    console.log('hr_leave-form setFormData called with data:', data)
    if (data) {
      // 标记数据是通过setFormData设置的
      isDataSetBySetFormData.value = true

      // 重置表单数据
      resetForm()

      // 设置新数据，确保时间字段正确处理
      Object.assign(formData, data)

      // 处理附件数据
      if (formData.attachment) {
        if (typeof formData.attachment === 'string') {
          try {
            formData.attachment = JSON.parse(formData.attachment)
          } catch {
            formData.attachment = []
          }
        }


      }

      // 显示对话框
      dialogVisible.value = true
      console.log('hr_leave-form 表单数据已设置，ID:', formData.id)
      console.log('hr_leave-form 时间字段:', {
        start_time: formData.start_time,
        end_time: formData.end_time
      })
    }
  }

  // 暴露方法供FormManager调用
  defineExpose({
    showForm,
    resetForm,
    loadFormData,
    updateFormData,
    setFormData
  })
</script>

<style scoped lang="scss">
  .hr-leave-dialog {
    // 参考文章页面的对话框样式
    :deep(.el-dialog) {
      margin-top: 5vh !important;
      margin-bottom: 5vh !important;
      display: flex;
      flex-direction: column;
      max-height: 90vh;
    }

    :deep(.el-dialog__body) {
      overflow: auto;
      padding: 20px;
      max-height: 65vh;
    }

    .dialog-content {
      max-height: 60vh;
      padding: 10px 30px;
      overflow-y: auto;
      border-bottom: 1px solid #eaeaea;
    }

    .form-status-bar {
      text-align: center;
      margin-bottom: 20px;

      .el-tag {
        font-size: 14px;
        padding: 8px 16px;
      }
    }

    .form-section {
      margin-bottom: 32px;

      .section-title {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 16px;
        font-size: 16px;
        font-weight: 600;
        color: #409eff;
        border-left: 4px solid #409eff;
        padding-left: 12px;
      }

      .field-tip {
        font-size: 12px;
        color: #909399;
        margin-top: 4px;
      }
    }

    .leave-form {
      .el-form-item {
        margin-bottom: 20px;
      }

      .el-textarea {
        .el-textarea__inner {
          resize: vertical;
        }
      }
    }

    .dialog-footer {
      display: flex;
      justify-content: center;
      gap: 16px;

      .el-button {
        min-width: 100px;
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .hr-leave-dialog {
      .dialog-footer {
        flex-direction: column;
        align-items: center;

        .el-button {
          width: 100%;
          max-width: 200px;
        }
      }
    }
  }
</style>
