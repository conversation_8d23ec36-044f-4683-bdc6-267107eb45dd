<?php

namespace app\hr\service;

use app\common\core\base\BaseService;
use app\common\core\crud\traits\CrudServiceTrait;
use app\common\exception\BusinessException;
use app\common\utils\DateCalculator;
use app\system\model\AdminModel;
use app\workflow\constants\WorkflowStatusConstant;
use app\workflow\interfaces\FormServiceInterface;
use app\hr\model\HrBusinessTrip;
use app\hr\model\HrBusinessTripItinerary;
use think\facade\Db;

class HrBusinessTripService extends BaseService implements FormServiceInterface
{
	use CrudServiceTrait;
	
	public function __construct()
	{
		$this->model = new HrBusinessTrip();
		parent::__construct();
	}
	
	/**
	 * 获取表单数据
	 */
	public function getFormData(int $id): array
	{
		$businessTrip = $this->model->with([
			'items',
			'submitter'
		])
		                            ->findOrEmpty($id);
		if ($businessTrip->isEmpty()) {
			throw new BusinessException('出差申请不存在');
		}
		$companions = $businessTrip->companions;
		if (!empty($companions)) {
			$ids                        = array_column($companions, 'id');
			$businessTrip['companions'] = AdminModel::where('id', 'in', $ids)
			                                        ->field('real_name as name,id,status,gender')
			                                        ->select()
			                                        ->toArray();
		}
		return $businessTrip->toArray();
	}
	
	/**
	 * 保存表单数据
	 */
	public function saveForm(array $data): array
	{
		$this->model->startTrans();
		try {
			$formData                         = $data['business_data'];
			$formData['approval_status']      = WorkflowStatusConstant::STATUS_DRAFT;
			$formData['workflow_instance_id'] = 0;
			$formData['submitter_id']         = $data['submitter_id'] ?? get_user_id();
			$this->validateFormData($formData);
			$hrBusinessTripModel = new HrBusinessTrip();
			$businessTripId      = $hrBusinessTripModel->saveByCreate($formData);
			
			// 保存行程明细
			if (!empty($formData['items'])) {
				$this->saveItineraries($businessTripId, $formData['items']);
			}
			
			// 更新汇总字段
			$this->updateSummaryFields($hrBusinessTripModel);
			
			$this->model->commit();
			
			// 返回完整数据
			$formData = $this->getFormData($businessTripId);
			return [
				$businessTripId,
				$formData
			];
			
		}
		catch (\Exception $e) {
			$this->model->rollback();
			throw new BusinessException('保存失败：' . $e->getMessage());
		}
	}
	
	/**
	 * 更新表单数据
	 */
	public function updateForm(int $id, array $data): bool
	{
		$this->model->startTrans();
		try {
			$this->validateFormData($data);
			// 更新主表
			$info = $this->model->findOrEmpty($id);
			if ($info->isEmpty()) {
				throw new BusinessException('出差申请不存在');
			}
			$res = $info->saveByUpdate($data);
			
			// 更新行程明细
			if (isset($data['items'])) {
				$this->updateItineraries($id, $data['items']);
			}
			
			// 更新汇总字段
			$this->updateSummaryFields($info);
			
			$this->model->commit();
			return true;
			
		}
		catch (\Exception $e) {
			$this->model->rollback();
			throw new BusinessException('更新失败：' . $e->getMessage());
		}
	}
	
	/**
	 * 删除表单数据
	 */
	public function deleteForm(int $id): bool
	{
		$businessTrip = $this->model->find($id);
		if (!$businessTrip) {
			throw new BusinessException('出差申请不存在');
		}
		
		// 检查是否可以删除
		if ($businessTrip->approval_status != 0) {
			throw new BusinessException('只能删除草稿状态的申请');
		}
		
		$this->model->startTrans();
		try {
			// 删除行程明细
			HrBusinessTripItinerary::where('business_trip_id', $id)
			                       ->delete();
			
			// 删除主表
			$businessTrip->delete();
			
			$this->model->commit();
			return true;
			
		}
		catch (\Exception $e) {
			$this->model->rollback();
			throw new BusinessException('删除失败：' . $e->getMessage());
		}
	}
	
	/**
	 * 更新表单状态
	 */
	public function updateFormStatus(int $id, int $status, array $extra = []): bool
	{
		$updateData = ['approval_status' => $status];
		
		// 根据状态添加时间戳
		switch ($status) {
			case WorkflowStatusConstant::STATUS_PROCESSING:
				$updateData['submit_time'] = date('Y-m-d H:i:s');
				break;
			case WorkflowStatusConstant::STATUS_COMPLETED:
				$updateData['approval_time'] = date('Y-m-d H:i:s');
				break;
			case WorkflowStatusConstant::STATUS_RECALLED:
				$updateData['recall_time'] = date('Y-m-d H:i:s');
				break;
			case WorkflowStatusConstant::STATUS_VOID:
				$updateData['void_time'] = date('Y-m-d H:i:s');
				if (!empty($extra['reason'])) {
					$updateData['void_reason'] = $extra['reason'];
				}
				break;
		}
		
		// 添加额外字段
		if (!empty($extra['workflow_instance_id'])) {
			$updateData['workflow_instance_id'] = $extra['workflow_instance_id'];
		}
		if (!empty($extra['submitter_id'])) {
			$updateData['submitter_id'] = $extra['submitter_id'];
		}
		$info = HrBusinessTrip::findOrEmpty($id);
		if ($info->isEmpty()) {
			throw new BusinessException('出差申请不存在');
		}
		return $info->saveByUpdate($updateData);
	}
	
	/**
	 * 获取实例标题
	 */
	public function getInstanceTitle($formData): string
	{
		$submitterName = $formData['submitter_name'] ?? '';
		$duration      = $formData['duration'] ?? 0;
		
		return "{$submitterName}出差申请({$duration}天)";
	}
	
	/**
	 * 验证表单数据
	 */
	public function validateFormData(array $data, string $scene = 'create'): array
	{
		$rules = [
			//			'start_time' => 'require|dateFormat:Y-m-d H:i:s',
			//			'end_time'   => 'require|dateFormat:Y-m-d H:i:s',
			//			'dept_id'    => 'require|integer',
			'purpose' => 'require|max:500',
		];
		
		$messages = [
			//			'start_time.require'    => '请选择开始时间',
			//			'start_time.dateFormat' => '开始时间格式错误',
			//			'end_time.require'      => '请选择结束时间',
			//			'end_time.dateFormat'   => '结束时间格式错误',
			//			'dept_id.require'       => '请选择部门',
			//			'dept_id.integer'       => '部门格式错误',
			'purpose.require' => '请填写出差事由',
			'purpose.max'     => '出差事由长度不能超过500个字符',
		];
		
		$validate = validate($rules, $messages);
		if (!$validate->check($data)) {
			throw new BusinessException($validate->getError());
		}
		
		// 验证明细项
		if (empty($data['items']) || !is_array($data['items'])) {
			throw new BusinessException('请至少添加一条行程明细');
		}
		
		// 验证每个明细项
		foreach ($data['items'] as $index => $item) {
			$rowNum = $index + 1;
			
			if (empty($item['transport_type'])) {
				throw new BusinessException("第{$rowNum}行：请选择交通工具");
			}
			
			if (empty($item['trip_mode'])) {
				throw new BusinessException("第{$rowNum}行：请选择单程往返");
			}
			
			if (empty($item['departure_city'])) {
				throw new BusinessException("第{$rowNum}行：请输入出发地");
			}
			
			if (empty($item['destination_city'])) {
				throw new BusinessException("第{$rowNum}行：请输入目的地");
			}
			
			if (empty($item['start_time'])) {
				throw new BusinessException("第{$rowNum}行：请选择开始时间");
			}
			
			if (empty($item['end_time'])) {
				throw new BusinessException("第{$rowNum}行：请选择结束时间");
			}
			
			// 验证时间逻辑
			if (strtotime($item['end_time']) <= strtotime($item['start_time'])) {
				throw new BusinessException("第{$rowNum}行：结束时间必须晚于开始时间");
			}
		}
		
		// 计算并验证总时长
		$calculatedDuration = $this->calculateBusinessTripDuration($data['items']);
		$submittedDuration  = floatval($data['duration'] ?? 0);

		// 允许0.5小时的误差（考虑浮点数精度问题和半小时规则）
		if (abs($calculatedDuration - $submittedDuration) > 0.5) {
			throw new BusinessException("出差时长不匹配：计算值为{$calculatedDuration}小时，提交值为{$submittedDuration}小时");
		}
		
		return $data;
	}
	
	/**
	 * 计算出差总时长（小时）
	 * 使用半小时向上取整规则
	 *
	 * ⚠️ 重要：与前端使用相同的算法
	 * 使用统一的DateCalculator工具类确保前后端一致性
	 */
	private function calculateBusinessTripDuration(array $items): float
	{
		if (empty($items)) {
			return 0;
		}

		$totalHours = 0;

		// 遍历所有明细项，累计计算时长
		foreach ($items as $item) {
			if (!empty($item['start_time']) && !empty($item['end_time'])) {
				// 使用新的半小时规则计算每个行程的时长
				$hours = DateCalculator::calculateHoursWithHalfHourRule(
					$item['start_time'],
					$item['end_time']
				);
				$totalHours += $hours;
			}
		}

		return $totalHours;
		
		return 0;
	}
	
	/**
	 * 保存行程明细
	 */
	private function saveItineraries(int $businessTripId, array $itineraries): void
	{
		$itineraryModel = new HrBusinessTripItinerary();
		
		foreach ($itineraries as $index => $itinerary) {
			$itinerary['business_trip_id'] = $businessTripId;
			//			$itinerary['sequence_no'] = $index + 1;
			//			$itinerary['tenant_id'] = request()->tenant_id;
			
			$itineraryModel->saveByCreate($itinerary);
		}
	}
	
	/**
	 * 更新行程明细
	 */
	private function updateItineraries(int $businessTripId, array $itineraries): void
	{
		// 删除原有明细
		$list = HrBusinessTripItinerary::where('business_trip_id', $businessTripId)
		                               ->select();
		$res  = $list->delete();
		// 重新保存
		$this->saveItineraries($businessTripId, $itineraries);
	}
	
	/**
	 * 更新汇总字段
	 */
	private function updateSummaryFields(HrBusinessTrip $info): void
	{
		$itineraries = HrBusinessTripItinerary::where('business_trip_id', $info['id'])
			//		                                      ->order('sequence_no')
			                                  ->select();
		
		// 转换为数组格式，以便使用统一的计算方法
		$items = [];
		foreach ($itineraries as $itinerary) {
			$items[] = [
				'start_time' => $itinerary->start_time,
				'end_time'   => $itinerary->end_time,
			];
		}
		
		// 使用统一的算法计算总天数
		$totalDuration = $this->calculateBusinessTripDuration($items);
		
		$info->saveByUpdate([
			'duration' => $totalDuration
		]);
	}
	
	public function afterWorkflowStatusChange(int $businessId, int $status, array $extra = []): bool
	{
		// TODO: Implement afterWorkflowStatusChange() method.
		return true;
	}
}