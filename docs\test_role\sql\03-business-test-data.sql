-- =====================================================
-- 权限测试 - 业务测试数据创建脚本
-- 创建日期：2025-01-31
-- 说明：创建CRM客户、线索等业务数据用于数据权限测试
-- =====================================================

-- 注意：请根据实际的表结构调整字段名称
-- 如果表不存在，请先创建相应的表结构

-- =====================================================
-- 1. CRM客户测试数据
-- 用于测试不同用户的数据权限范围
-- =====================================================

-- 检查表是否存在，如果不存在则跳过
-- SELECT 'CRM客户表测试数据创建' as message;

-- 清理现有测试数据（如果存在）
-- DELETE FROM crm_customer WHERE tenant_id = 1;

-- 创建客户测试数据（请根据实际表结构调整）
/*
INSERT INTO `crm_customer` (`id`, `name`, `phone`, `email`, `source`, `industry`, `level`, `status`, `remark`, `creator_id`, `dept_id`, `tenant_id`, `created_at`, `updated_at`) VALUES
-- 销售部经理创建的客户
(1001, '客户A-销售部经理', '13800001001', '<EMAIL>', 1, '互联网', 1, 1, '销售部经理创建', 202, 102, 1, NOW(), NOW()),
(1002, '客户B-销售部经理', '13800001002', '<EMAIL>', 2, '制造业', 2, 1, '销售部经理创建', 202, 102, 1, NOW(), NOW()),

-- 销售一组组长创建的客户
(1003, '客户C-销售一组长', '13800001003', '<EMAIL>', 1, '金融', 1, 1, '销售一组长创建', 203, 103, 1, NOW(), NOW()),
(1004, '客户D-销售一组长', '13800001004', '<EMAIL>', 3, '教育', 2, 1, '销售一组长创建', 203, 103, 1, NOW(), NOW()),

-- 销售一组员工创建的客户
(1005, '客户E-销售一组员工', '13800001005', '<EMAIL>', 1, '医疗', 1, 1, '销售一组员工创建', 204, 103, 1, NOW(), NOW()),
(1006, '客户F-销售一组员工', '13800001006', '<EMAIL>', 2, '零售', 3, 1, '销售一组员工创建', 204, 103, 1, NOW(), NOW()),

-- 销售二组员工创建的客户
(1007, '客户G-销售二组员工', '13800001007', '<EMAIL>', 1, '物流', 1, 1, '销售二组员工创建', 205, 104, 1, NOW(), NOW()),
(1008, '客户H-销售二组员工', '13800001008', '<EMAIL>', 3, '房地产', 2, 1, '销售二组员工创建', 205, 104, 1, NOW(), NOW()),

-- 技术部经理创建的客户
(1009, '客户I-技术部经理', '13800001009', '<EMAIL>', 2, '软件', 1, 1, '技术部经理创建', 206, 105, 1, NOW(), NOW()),

-- 技术部员工创建的客户
(1010, '客户J-技术部员工', '13800001010', '<EMAIL>', 1, '硬件', 2, 1, '技术部员工创建', 207, 106, 1, NOW(), NOW()),

-- 财务部员工创建的客户
(1011, '客户K-财务部员工', '13800001011', '<EMAIL>', 2, '咨询', 1, 1, '财务部员工创建', 208, 108, 1, NOW(), NOW()),

-- 自定义权限用户创建的客户
(1012, '客户L-自定义权限', '13800001012', '<EMAIL>', 1, '广告', 3, 1, '自定义权限用户创建', 209, 105, 1, NOW(), NOW());
*/

-- =====================================================
-- 2. CRM线索测试数据
-- 用于测试不同用户的数据权限范围
-- =====================================================

-- 检查表是否存在，如果不存在则跳过
-- SELECT 'CRM线索表测试数据创建' as message;

-- 清理现有测试数据（如果存在）
-- DELETE FROM crm_lead WHERE tenant_id = 1;

-- 创建线索测试数据（请根据实际表结构调整）
/*
INSERT INTO `crm_lead` (`id`, `name`, `phone`, `email`, `source`, `industry`, `status`, `remark`, `creator_id`, `dept_id`, `tenant_id`, `created_at`, `updated_at`) VALUES
-- 不同用户创建的线索数据
(2001, '线索A-销售经理', '13900001001', '<EMAIL>', 1, '互联网', 1, '销售经理创建', 202, 102, 1, NOW(), NOW()),
(2002, '线索B-销售组长', '13900001002', '<EMAIL>', 2, '制造业', 1, '销售组长创建', 203, 103, 1, NOW(), NOW()),
(2003, '线索C-销售员工1', '13900001003', '<EMAIL>', 1, '金融', 1, '销售员工1创建', 204, 103, 1, NOW(), NOW()),
(2004, '线索D-销售员工2', '13900001004', '<EMAIL>', 3, '教育', 1, '销售员工2创建', 205, 104, 1, NOW(), NOW()),
(2005, '线索E-技术经理', '13900001005', '<EMAIL>', 2, '软件', 1, '技术经理创建', 206, 105, 1, NOW(), NOW()),
(2006, '线索F-技术员工', '13900001006', '<EMAIL>', 1, '硬件', 1, '技术员工创建', 207, 106, 1, NOW(), NOW()),
(2007, '线索G-财务员工', '13900001007', '<EMAIL>', 2, '咨询', 1, '财务员工创建', 208, 108, 1, NOW(), NOW()),
(2008, '线索H-自定义权限', '13900001008', '<EMAIL>', 1, '广告', 1, '自定义权限创建', 209, 105, 1, NOW(), NOW());
*/

-- =====================================================
-- 3. 项目管理测试数据（如果有项目管理模块）
-- =====================================================

-- 检查表是否存在，如果不存在则跳过
-- SELECT '项目管理测试数据创建' as message;

-- 创建项目测试数据（请根据实际表结构调整）
/*
INSERT INTO `project_project` (`id`, `name`, `description`, `status`, `start_date`, `end_date`, `creator_id`, `tenant_id`, `created_at`, `updated_at`) VALUES
(3001, '项目A-技术部经理', '技术部经理创建的项目', 1, '2025-01-01', '2025-06-30', 206, 1, NOW(), NOW()),
(3002, '项目B-技术员工', '技术员工创建的项目', 1, '2025-02-01', '2025-07-31', 207, 1, NOW(), NOW()),
(3003, '项目C-自定义权限', '自定义权限用户创建的项目', 1, '2025-03-01', '2025-08-31', 209, 1, NOW(), NOW());
*/

-- =====================================================
-- 4. 每日报价测试数据（如果有报价模块）
-- =====================================================

-- 检查表是否存在，如果不存在则跳过
-- SELECT '每日报价测试数据创建' as message;

-- 创建报价测试数据（请根据实际表结构调整）
/*
INSERT INTO `daily_price_order` (`id`, `order_no`, `customer_name`, `product_name`, `price`, `quantity`, `total_amount`, `status`, `creator_id`, `tenant_id`, `created_at`, `updated_at`) VALUES
(4001, 'PO20250131001', '客户A', '产品A', 100.00, 10, 1000.00, 1, 202, 1, NOW(), NOW()),
(4002, 'PO20250131002', '客户B', '产品B', 200.00, 5, 1000.00, 1, 203, 1, NOW(), NOW()),
(4003, 'PO20250131003', '客户C', '产品C', 150.00, 8, 1200.00, 1, 204, 1, NOW(), NOW()),
(4004, 'PO20250131004', '客户D', '产品D', 300.00, 3, 900.00, 1, 205, 1, NOW(), NOW());
*/

-- =====================================================
-- 数据权限测试验证SQL
-- =====================================================

SELECT '=== 数据权限测试验证 ===' as message;

-- 显示测试用户信息
SELECT 
    '测试用户信息' as type,
    a.id,
    a.username,
    a.real_name,
    d.name as dept_name,
    r.name as role_name,
    CASE r.data_scope
        WHEN 1 THEN '全部数据'
        WHEN 2 THEN '本部门'
        WHEN 3 THEN '本部门及以下'
        WHEN 4 THEN '仅本人'
        WHEN 5 THEN '自定义'
    END as data_scope_text
FROM system_admin a
LEFT JOIN system_dept d ON a.dept_id = d.id
LEFT JOIN system_admin_role ar ON a.id = ar.admin_id
LEFT JOIN system_role r ON ar.role_id = r.id
WHERE a.tenant_id = 1
ORDER BY a.id;

-- =====================================================
-- 数据权限测试查询示例
-- =====================================================

-- 以下是各种数据权限的测试查询示例，请根据实际业务表调整

-- 1. 全部数据权限测试（租户超级管理员 - user_id=201）
/*
SELECT '全部数据权限测试' as test_type, COUNT(*) as record_count
FROM crm_customer 
WHERE tenant_id = 1;
-- 预期结果：12条记录
*/

-- 2. 本部门及以下权限测试（销售部经理 - user_id=202, dept_id=102）
/*
SELECT '销售部经理权限测试' as test_type, COUNT(*) as record_count
FROM crm_customer c
LEFT JOIN system_admin a ON c.creator_id = a.id
WHERE c.tenant_id = 1 
  AND a.dept_id IN (102, 103, 104);  -- 销售部及下级部门
-- 预期结果：8条记录
*/

-- 3. 本部门权限测试（销售一组长 - user_id=203, dept_id=103）
/*
SELECT '销售一组长权限测试' as test_type, COUNT(*) as record_count
FROM crm_customer c
LEFT JOIN system_admin a ON c.creator_id = a.id
WHERE c.tenant_id = 1 
  AND a.dept_id = 103;  -- 仅销售一组
-- 预期结果：4条记录
*/

-- 4. 仅本人权限测试（销售一组员工 - user_id=204）
/*
SELECT '销售一组员工权限测试' as test_type, COUNT(*) as record_count
FROM crm_customer 
WHERE tenant_id = 1 
  AND creator_id = 204;  -- 仅本人创建
-- 预期结果：2条记录
*/

-- 5. 自定义权限测试（自定义权限用户 - user_id=209, 权限部门：102,105）
/*
SELECT '自定义权限测试' as test_type, COUNT(*) as record_count
FROM crm_customer c
LEFT JOIN system_admin a ON c.creator_id = a.id
WHERE c.tenant_id = 1 
  AND a.dept_id IN (102, 105);  -- 销售部+技术部
-- 预期结果：4条记录
*/

SELECT '=== 请根据实际表结构执行上述测试查询 ===' as note;
SELECT '=== 如果表不存在，请先创建相应的业务表 ===' as note;
