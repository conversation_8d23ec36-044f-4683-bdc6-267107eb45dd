<template>
  <div class="monthly-stats-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>HR月度统计</span>
          <div class="header-controls">
            <el-date-picker
              v-model="selectedMonth"
              type="month"
              placeholder="选择月份"
              format="YYYY-MM"
              value-format="YYYY-MM"
              @change="onMonthChange"
            />
            <!--            <el-button type="primary" @click="exportStats" :loading="exporting">
                          导出统计
                        </el-button>-->
          </div>
        </div>
      </template>

      <!-- 个人统计 -->
      <div class="stats-section">
        <h3>汇总数据</h3>
        <div class="stats-cards">
          <el-card class="stats-card">
            <div class="stats-item">
              <div class="stats-label">请假时长</div>
              <div class="stats-value">{{ personalStats.leave_stats?.total_hours || 0 }}小时</div>
            </div>
          </el-card>
          <el-card class="stats-card">
            <div class="stats-item">
              <div class="stats-label">外出时长</div>
              <div class="stats-value">{{ personalStats.outing_hours || 0 }}小时</div>
            </div>
          </el-card>
          <el-card class="stats-card">
            <div class="stats-item">
              <div class="stats-label">出差时长</div>
              <div class="stats-value">{{ personalStats.business_trip_hours || 0 }}小时</div>
            </div>
          </el-card>
          <el-card class="stats-card total">
            <div class="stats-item">
              <div class="stats-label">总计</div>
              <div class="stats-value">{{ personalStats.display_format || '0小时' }}</div>
            </div>
          </el-card>
        </div>
      </div>

      <!-- 请假类型详细统计 -->
      <div class="stats-section" v-if="personalStats.leave_stats && hasLeaveData">
        <h3>请假类型统计</h3>
        <el-table :data="leaveTypeStats" style="width: 100%">
          <el-table-column prop="name" label="请假类型" />
          <el-table-column prop="hours" label="时长(小时)" />
          <el-table-column label="占比">
            <template #default="{ row }">
              {{ calculatePercentage(row.hours, personalStats.leave_stats.total_hours) }}
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 统计规则说明 -->
      <div class="stats-section">
        <h3>统计规则</h3>
        <el-descriptions :column="1" border>
          <el-descriptions-item label="计算单位">半小时为最小单位</el-descriptions-item>
          <el-descriptions-item label="取整规则"
            >不足0.5小时按0.5小时，大于0.5小时小于1小时按1小时计算
          </el-descriptions-item>
          <el-descriptions-item label="工作时间">{{
            workTimeConfig.description || '每日8小时'
          }}</el-descriptions-item>
          <el-descriptions-item label="显示格式"
            >超过每日工作时间时显示为"X天Y小时"格式</el-descriptions-item
          >
        </el-descriptions>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, computed } from 'vue'
  import { ElMessage } from 'element-plus'
  import {
    HrMonthlyStatsApi,
    type HrMonthlyStatistics,
    type HrStatsConfig,
    calculatePercentage
  } from '@/api/hr/hrMonthlyStats'

  // 响应式数据
  const selectedMonth = ref(new Date().toISOString().slice(0, 7))
  const personalStats = ref<HrMonthlyStatistics>({} as HrMonthlyStatistics)
  const workTimeConfig = ref<HrStatsConfig['work_time_config']>(
    {} as HrStatsConfig['work_time_config']
  )
  const loading = ref(false)
  const exporting = ref(false)

  // 计算属性
  const leaveTypeStats = computed(() => {
    if (!personalStats.value.leave_stats?.by_type) return []

    return Object.entries(personalStats.value.leave_stats.by_type)
      .filter(([_, data]) => data.hours > 0)
      .map(([type, data]) => ({
        type: parseInt(type),
        name: data.name,
        hours: data.hours
      }))
  })

  const hasLeaveData = computed(() => {
    return personalStats.value.leave_stats?.total_hours > 0
  })

  // 方法
  const loadPersonalStats = async () => {
    try {
      loading.value = true
      const res = await HrMonthlyStatsApi.getEmployeeStats({
        year_month: selectedMonth.value
      })
      if (res.code === 1) {
        personalStats.value = res.data
      } else {
        ElMessage.error(res.message || '获取统计数据失败')
      }
    } catch (error) {
      console.error('加载统计数据失败:', error)
      ElMessage.error('加载统计数据失败')
    } finally {
      loading.value = false
    }
  }

  const loadStatsConfig = async () => {
    try {
      const res = await HrMonthlyStatsApi.getStatsConfig()
      if (res.code === 1) {
        workTimeConfig.value = res.data.work_time_config
      }
    } catch (error) {
      console.error('加载配置信息失败:', error)
    }
  }

  const onMonthChange = () => {
    loadPersonalStats()
  }

  const exportStats = async () => {
    try {
      exporting.value = true
      const res = await HrMonthlyStatsApi.exportEmployeeStats({
        year_month: selectedMonth.value
      })
      if (res.code === 1) {
        // 这里可以实现具体的导出逻辑，比如下载Excel文件
        ElMessage.success('导出成功')
        console.log('导出数据:', res.data)
      } else {
        ElMessage.error(res.message || '导出失败')
      }
    } catch (error) {
      console.error('导出失败:', error)
      ElMessage.error('导出失败')
    } finally {
      exporting.value = false
    }
  }

  // 生命周期
  onMounted(() => {
    loadPersonalStats()
    loadStatsConfig()
  })
</script>

<style scoped>
  .monthly-stats-container {
    padding: 20px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .header-controls {
    display: flex;
    gap: 12px;
    align-items: center;
  }

  .stats-section {
    margin-bottom: 24px;
  }

  .stats-section h3 {
    margin-bottom: 16px;
    color: #303133;
    font-size: 16px;
    font-weight: 600;
  }

  .stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
  }

  .stats-card {
    text-align: center;
    cursor: default;
  }

  .stats-card.total {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
  }

  .stats-card.total :deep(.el-card__body) {
    color: white;
  }

  .stats-item {
    padding: 8px 0;
  }

  .stats-label {
    font-size: 14px;
    color: #909399;
    margin-bottom: 8px;
  }

  .stats-card.total .stats-label {
    color: rgba(255, 255, 255, 0.8);
  }

  .stats-value {
    font-size: 24px;
    font-weight: 600;
    color: #303133;
  }

  .stats-card.total .stats-value {
    color: white;
  }

  :deep(.el-descriptions__label) {
    font-weight: 600;
  }
</style>
