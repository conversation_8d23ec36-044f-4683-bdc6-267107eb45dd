<template>
  <div class="hr-stats">
    <!-- 统计卡片 -->
    <StatsCardList :stats-data="overviewStats" :loading="loading"></StatsCardList>

    <!-- 统计图表和概览 -->
    <div class="column column2">
      <StatsChart :chart-data="chartData" :loading="loading"></StatsChart>
      <StatsOverview :overview-data="overviewStats" :loading="loading"></StatsOverview>
    </div>

    <!-- 员工统计表格 -->
    <div class="column column3">
      <EmployeeStatsTable
        :employee-data="employeeStats"
        :loading="loading"
        :selected-month="selectedMonth"
        @month-change="onMonthChange"
        @export="exportStats"
      ></EmployeeStatsTable>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, computed } from 'vue'
  import { ElMessage } from 'element-plus'
  import StatsCardList from './widget/StatsCardList.vue'
  import StatsChart from './widget/StatsChart.vue'
  import StatsOverview from './widget/StatsOverview.vue'
  import EmployeeStatsTable from './widget/EmployeeStatsTable.vue'
  import { useCommon } from '@/composables/useCommon'
  import {
    HrMonthlyStatsApi,
    type HrMonthlyStatistics,
    type HrStatsConfig,
    calculatePercentage
  } from '@/api/hr/hrMonthlyStats'

  // 滚动到顶部
  useCommon().scrollToTop()

  // 响应式数据
  const selectedMonth = ref(new Date().toISOString().slice(0, 7))
  const personalStats = ref<HrMonthlyStatistics>({} as HrMonthlyStatistics)
  const employeeStats = ref<any[]>([])
  const overviewStats = ref<any>({})
  const chartData = ref<any>({})
  const workTimeConfig = ref<HrStatsConfig['work_time_config']>(
    {} as HrStatsConfig['work_time_config']
  )
  const loading = ref(false)
  const exporting = ref(false)

  // 计算属性
  const leaveTypeStats = computed(() => {
    if (!personalStats.value.leave_stats?.by_type) return []

    return Object.entries(personalStats.value.leave_stats.by_type)
      .filter(([_, data]) => data.hours > 0)
      .map(([type, data]) => ({
        type: parseInt(type),
        name: data.name,
        hours: data.hours
      }))
  })

  const hasLeaveData = computed(() => {
    return personalStats.value.leave_stats?.total_hours > 0
  })

  // 方法
  const loadPersonalStats = async () => {
    try {
      loading.value = true
      const res = await HrMonthlyStatsApi.getEmployeeStats({
        year_month: selectedMonth.value
      })
      if (res.code === 1) {
        personalStats.value = res.data
      } else {
        ElMessage.error(res.message || '获取统计数据失败')
      }
    } catch (error) {
      console.error('加载统计数据失败:', error)
      ElMessage.error('加载统计数据失败')
    } finally {
      loading.value = false
    }
  }

  // 加载概览统计数据
  const loadOverviewStats = async () => {
    try {
      const res = await HrMonthlyStatsApi.getStatsOverview({
        year_month: selectedMonth.value
      })
      if (res.code === 1) {
        overviewStats.value = res.data
        // 构建图表数据
        chartData.value = {
          leave_hours: res.data.leave_hours || 0,
          outing_hours: res.data.outing_hours || 0,
          business_trip_hours: res.data.business_trip_hours || 0
        }
      }
    } catch (error) {
      console.error('加载概览数据失败:', error)
    }
  }

  // 加载所有员工统计数据
  const loadAllEmployeeStats = async () => {
    try {
      const res = await HrMonthlyStatsApi.getAllEmployeeStats({
        year_month: selectedMonth.value,
        page: 1,
        limit: 50 // 暂时获取前50个员工
      })
      if (res.code === 1) {
        employeeStats.value = res.data.data
      } else {
        ElMessage.error(res.message || '获取员工统计数据失败')
      }
    } catch (error) {
      console.error('加载员工统计数据失败:', error)
      ElMessage.error('加载员工统计数据失败')
    }
  }

  const loadStatsConfig = async () => {
    try {
      const res = await HrMonthlyStatsApi.getStatsConfig()
      if (res.code === 1) {
        workTimeConfig.value = res.data.work_time_config
      }
    } catch (error) {
      console.error('加载配置信息失败:', error)
    }
  }

  const onMonthChange = (month?: string) => {
    if (month) {
      selectedMonth.value = month
    }
    loadPersonalStats()
    loadOverviewStats()
    loadAllEmployeeStats()
  }

  const exportStats = async () => {
    try {
      exporting.value = true
      const res = await HrMonthlyStatsApi.exportEmployeeStats({
        year_month: selectedMonth.value
      })
      if (res.code === 1) {
        // 这里可以实现具体的导出逻辑，比如下载Excel文件
        ElMessage.success('导出成功')
        console.log('导出数据:', res.data)
      } else {
        ElMessage.error(res.message || '导出失败')
      }
    } catch (error) {
      console.error('导出失败:', error)
      ElMessage.error('导出失败')
    } finally {
      exporting.value = false
    }
  }

  // 生命周期
  onMounted(() => {
    loadPersonalStats()
    loadOverviewStats()
    loadAllEmployeeStats()
    loadStatsConfig()
  })
</script>

<style lang="scss" scoped>
  .hr-stats {
    padding-bottom: 15px;

    :deep(.card-header) {
      display: flex;
      justify-content: space-between;
      padding: 20px 25px 5px 0;

      .title {
        h4 {
          font-size: 18px;
          font-weight: 500;
          color: var(--art-text-gray-800);
        }

        p {
          margin-top: 3px;
          font-size: 13px;

          span {
            margin-left: 10px;
            color: #52c41a;
          }
        }
      }
    }

    // 主标题
    :deep(.box-title) {
      color: var(--art-gray-900) !important;
    }

    // 副标题
    :deep(.subtitle) {
      color: var(--art-gray-600) !important;
    }

    :deep(.card-list li),
    .region {
      background: var(--art-main-bg-color);
      border-radius: calc(var(--custom-radius) + 4px) !important;
    }

    .column {
      display: flex;
      justify-content: space-between;
      margin-top: var(--console-margin);
      background-color: transparent !important;
    }

    .column2 {
      :deep(.stats-chart) {
        width: 60%;
      }

      :deep(.stats-overview) {
        width: calc(40% - 15px);
      }
    }

    .column3 {
      :deep(.employee-stats-table) {
        width: 100%;
      }
    }
  }
</style>

<!-- 移动端处理 -->
<style lang="scss" scoped>
  .hr-stats {
    @media screen and (max-width: $device-ipad-pro) {
      .column2 {
        margin-top: 15px;

        :deep(.stats-chart) {
          width: 50%;
        }

        :deep(.stats-overview) {
          width: calc(50% - 15px);
        }
      }

      .column3 {
        margin-top: 15px;

        :deep(.employee-stats-table) {
          width: 100%;
        }
      }
    }

    @media screen and (max-width: $device-ipad-vertical) {
      :deep(.card-list) {
        width: calc(100% + 15px);
        margin-left: -15px;

        li {
          width: calc(50% - 15px);
          margin: 0 0 15px 15px;
        }
      }

      .column2 {
        display: block;
        margin-top: 0;

        :deep(.stats-chart) {
          width: 100%;
        }

        :deep(.stats-overview) {
          width: 100%;
          margin-top: 15px;
        }
      }

      .column3 {
        display: block;
        margin-top: 15px;

        :deep(.employee-stats-table) {
          width: 100%;
          margin-top: 15px;
        }
      }
    }

    @media screen and (max-width: $device-phone) {
      :deep(.card-list) {
        width: 100%;
        margin: 0;

        li {
          width: 100%;
          margin: 0 0 15px;
        }
      }
    }
  }
</style>
