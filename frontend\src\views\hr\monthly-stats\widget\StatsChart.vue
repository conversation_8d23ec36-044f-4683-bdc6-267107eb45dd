<template>
  <div class="region stats-chart art-custom-card">
    <div class="card-header">
      <div class="title">
        <h4 class="box-title">时长分布</h4>
        <p class="subtitle">各类型时长占比统计</p>
      </div>
    </div>
    <div class="chart-container" v-loading="loading">
      <div ref="chartRef" class="chart"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, watch, nextTick } from 'vue'
  import * as echarts from 'echarts'
  import { useSettingStore } from '@/store/modules/setting'

  interface Props {
    chartData?: any
    loading?: boolean
  }

  const props = withDefaults(defineProps<Props>(), {
    chartData: () => ({}),
    loading: false
  })

  const chartRef = ref<HTMLElement>()
  let chartInstance: echarts.ECharts | null = null
  const settingStore = useSettingStore()

  const initChart = () => {
    if (!chartRef.value) return

    chartInstance = echarts.init(chartRef.value)
    updateChart()
  }

  const updateChart = () => {
    if (!chartInstance) return

    const data = props.chartData
    const chartData = [
      { value: data.leave_hours || 0, name: '请假时长' },
      { value: data.outing_hours || 0, name: '外出时长' },
      { value: data.business_trip_hours || 0, name: '出差时长' }
    ].filter((item) => item.value > 0)

    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c}小时 ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        textStyle: {
          color: settingStore.systemThemeType === 'dark' ? '#fff' : '#333'
        }
      },
      series: [
        {
          name: '时长统计',
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['60%', '50%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 20,
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: chartData,
          color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de']
        }
      ]
    }

    chartInstance.setOption(option)
  }

  const resizeChart = () => {
    if (chartInstance) {
      chartInstance.resize()
    }
  }

  // 监听主题变化
  watch(
    () => settingStore.systemThemeType,
    () => {
      nextTick(() => {
        if (chartInstance) {
          chartInstance.dispose()
          initChart()
        }
      })
    }
  )

  // 监听数据变化
  watch(
    () => props.chartData,
    () => {
      updateChart()
    },
    { deep: true }
  )

  onMounted(() => {
    nextTick(() => {
      initChart()
      window.addEventListener('resize', resizeChart)
    })
  })

  onUnmounted(() => {
    if (chartInstance) {
      chartInstance.dispose()
    }
    window.removeEventListener('resize', resizeChart)
  })
</script>

<style lang="scss" scoped>
  .stats-chart {
    height: 400px;
    padding: 20px;

    .chart-container {
      height: calc(100% - 60px);

      .chart {
        width: 100%;
        height: 100%;
      }
    }
  }

  @media screen and (max-width: $device-ipad-vertical) {
    .stats-chart {
      height: 300px;
    }
  }
</style>
