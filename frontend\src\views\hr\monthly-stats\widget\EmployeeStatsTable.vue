<template>
  <div class="region employee-stats-table art-custom-card">
    <div class="card-header">
      <div class="title">
        <h4 class="box-title">员工统计 - {{ selectedMonth }}</h4>
      </div>
      <!-- 暂时屏蔽导出功能
      <div class="header-controls">
        <el-button type="primary" @click="onExport" :loading="exporting" size="default">
          <el-icon><Download /></el-icon>
          导出统计
        </el-button>
      </div>
      -->
    </div>

    <div class="table-container" v-loading="loading">
      <art-table
        :data="employeeData"
        :pagination="pagination"
        size="default"
        :border="true"
        :stripe="true"
        :show-header-background="true"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
        <template #default>
          <el-table-column label="序号" type="index" width="60" align="center" />

          <el-table-column label="员工姓名" prop="employee_name" min-width="120">
            <template #default="{ row }">
              <div class="employee-info">
                <el-avatar :size="32" :src="row.avatar || defaultAvatar">
                  {{ row.employee_name?.charAt(0) }}
                </el-avatar>
                <span class="name">{{ row.employee_name }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="部门" prop="department" min-width="100" />

          <el-table-column label="请假时长" prop="leave_hours" width="100" align="center">
            <template #default="{ row }">
              <el-tag type="info" size="small">{{ row.leave_hours || 0 }}h</el-tag>
            </template>
          </el-table-column>

          <el-table-column label="外出时长" prop="outing_hours" width="100" align="center">
            <template #default="{ row }">
              <el-tag type="success" size="small">{{ row.outing_hours || 0 }}h</el-tag>
            </template>
          </el-table-column>

          <el-table-column label="出差时长" prop="business_trip_hours" width="100" align="center">
            <template #default="{ row }">
              <el-tag type="warning" size="small">{{ row.business_trip_hours || 0 }}h</el-tag>
            </template>
          </el-table-column>

          <el-table-column label="总计时长" prop="total_hours" width="120" align="center">
            <template #default="{ row }">
              <div class="total-hours">
                <span class="hours">{{ row.total_hours || 0 }}h</span>
                <span class="format">{{ row.display_format || '0小时' }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="时长占比" width="150" align="center">
            <template #default="{ row }">
              <el-progress
                :percentage="calculatePercentage(row.total_hours)"
                :color="getProgressColor(row.total_hours)"
                :stroke-width="6"
                :show-text="false"
              />
              <span class="percentage-text">{{ calculatePercentage(row.total_hours) }}%</span>
            </template>
          </el-table-column>

          <!--          <el-table-column label="操作" width="120" align="center" fixed="right">-->
          <!--            <template #default="{ row }">-->
          <!--              <el-button type="primary" link size="small" @click="viewDetail(row)">-->
          <!--                查看详情-->
          <!--              </el-button>-->
          <!--            </template>-->
          <!--          </el-table-column>-->
        </template>
      </art-table>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ElMessage } from 'element-plus'
  // import { Download } from '@element-plus/icons-vue'
  import defaultAvatar from '@/assets/img/avatar/admin.png'

  interface Props {
    employeeData?: any[]
    loading?: boolean
    selectedMonth?: string
  }

  const props = withDefaults(defineProps<Props>(), {
    employeeData: () => [],
    loading: false,
    selectedMonth: ''
  })

  const emit = defineEmits<{
    monthChange: [month: string]
    export: []
  }>()

  const exporting = ref(false)
  const pagination = ref({
    currentPage: 1,
    pageSize: 10,
    total: 0
  })

  // 计算最大时长用于百分比计算
  const maxHours = computed(() => {
    if (!props.employeeData.length) return 1
    return Math.max(...props.employeeData.map((item) => item.total_hours || 0), 1)
  })

  const calculatePercentage = (hours: number) => {
    if (!hours || maxHours.value === 0) return 0
    return Math.round((hours / maxHours.value) * 100)
  }

  const getProgressColor = (hours: number) => {
    const percentage = calculatePercentage(hours)
    if (percentage >= 80) return '#f56c6c'
    if (percentage >= 60) return '#e6a23c'
    if (percentage >= 40) return '#409eff'
    return '#67c23a'
  }

  const onMonthChange = (month: string) => {
    emit('monthChange', month)
  }

  const onExport = () => {
    emit('export')
  }

  const viewDetail = (row: any) => {
    ElMessage.info(`查看 ${row.employee_name} 的详细统计`)
    // TODO: 实现详情查看功能
  }

  const handleSizeChange = (size: number) => {
    pagination.value.pageSize = size
    // TODO: 重新加载数据
  }

  const handleCurrentChange = (page: number) => {
    pagination.value.currentPage = page
    // TODO: 重新加载数据
  }
</script>

<style lang="scss" scoped>
  .employee-stats-table {
    padding: 20px;

    .header-controls {
      display: flex;
      gap: 12px;
      align-items: center;
    }

    .table-container {
      margin-top: 20px;
    }

    .employee-info {
      display: flex;
      align-items: center;
      gap: 10px;

      .name {
        font-weight: 500;
        color: var(--art-text-gray-800);
      }
    }

    .total-hours {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 2px;

      .hours {
        font-weight: 600;
        color: var(--art-text-gray-800);
      }

      .format {
        font-size: 12px;
        color: var(--art-text-gray-500);
      }
    }

    .percentage-text {
      display: block;
      margin-top: 4px;
      font-size: 12px;
      color: var(--art-text-gray-600);
    }
  }

  @media screen and (max-width: $device-ipad-vertical) {
    .employee-stats-table {
      .header-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
      }
    }
  }
</style>
