<template>
  <ElDialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="800px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    destroy-on-close
  >
    <div v-loading="loading">
      <ElForm
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        label-position="right"
      >
        <ElRow :gutter="20">
          <ElCol :span="12">
            <ElFormItem label="开始时间" prop="start_time">
              <ElDatePicker
                v-model="formData.start_time"
                type="datetime"
                placeholder="请选择开始时间"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm:ss"
                :disabled="!isEditable"
                @change="calculateDuration"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="结束时间" prop="end_time">
              <ElDatePicker
                v-model="formData.end_time"
                type="datetime"
                placeholder="请选择结束时间"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm:ss"
                :disabled="!isEditable"
                @change="calculateDuration"
              />
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElRow :gutter="20">
          <ElCol :span="12">
            <ElFormItem label="外出时长">
              <ElInput
                :value="formData.duration + ' 小时'"
                readonly
                style="width: 100%"
                placeholder="自动计算"
              />
              <div style="margin-top: 4px; color: #909399; font-size: 12px">
                按半小时向上取整规则自动计算（不足0.5小时按0.5小时计算）
              </div>
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElFormItem label="外出事由" prop="purpose">
          <ElInput
            v-model="formData.purpose"
            type="textarea"
            :rows="3"
            placeholder="请输入外出事由"
            :disabled="!isEditable"
            maxlength="500"
            show-word-limit
          />
        </ElFormItem>

        <ElFormItem label="附件">
          <FormUploader
            v-model="formData.attachment"
            :disabled="!isEditable"
            :limit="10"
            multiple
            accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png"
          />
        </ElFormItem>
      </ElForm>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="handleCancel">取消</ElButton>
        <ElButton v-if="isEditable" type="primary" :loading="saving" @click="handleSave">
          保存
        </ElButton>
        <ElButton v-if="isEditable" type="success" :loading="submitting" @click="handleSubmit">
          提交审批
        </ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
  import { ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
  import { ApplicationApi } from '@/api/workflow/ApplicationApi'
  import FormUploader from '@/components/custom/FormUploader/index.vue'
  import { calculateHoursWithHalfHourRule } from '@/utils/date'

  // 组件属性定义
  interface Props {
    modelValue: boolean
    formId?: number | string
    definitionId?: number | string
  }

  // 事件定义
  interface Emits {
    (e: 'update:modelValue', value: boolean): void

    (e: 'success', data: any): void

    (e: 'cancel'): void

    (e: 'save', data: any): void

    (e: 'submit', data: any): void
  }

  // 表单数据接口
  interface HrOutingFormData {
    id?: number
    start_time: string
    end_time: string
    duration: number
    purpose: string
    attachment: any[]
    approval_status?: number
    workflow_instance_id?: number
  }

  const props = withDefaults(defineProps<Props>(), {
    modelValue: false,
    formId: 0,
    definitionId: 0
  })

  const emit = defineEmits<Emits>()

  // ==================== 响应式数据 ====================

  /** 表单引用 */
  const formRef = ref<FormInstance>()

  /** 对话框显示状态 */
  const dialogVisible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
  })

  /** 对话框标题 */
  const dialogTitle = computed(() => {
    if (formData.id) {
      return `外出申请 - ${formData.approval_status_text || '草稿'}`
    }
    return '发起外出申请'
  })

  /** 表单数据 */
  const formData = reactive<HrOutingFormData & any>({
    start_time: '',
    end_time: '',
    duration: 0,
    purpose: '',
    attachment: [],
    approval_status: 0
  })

  /** 加载状态 */
  const loading = ref(false)
  const saving = ref(false)
  const submitting = ref(false)

  /** 是否可编辑 */
  const isEditable = computed(() => {
    return (
      !formData.approval_status || formData.approval_status === 0 || formData.approval_status === 3
    )
  })

  // ==================== 表单验证规则 ====================
  const formRules: FormRules = {
    start_time: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
    end_time: [{ required: true, message: '请选择结束时间', trigger: 'change' }],
    purpose: [{ required: true, message: '请输入外出事由', trigger: 'blur' }]
  }

  // ==================== 方法定义 ====================

  /**
   * 计算外出时长
   * 使用半小时向上取整规则
   */
  const calculateDuration = () => {
    if (formData.start_time && formData.end_time) {
      // 使用新的半小时规则计算
      formData.duration = calculateHoursWithHalfHourRule(formData.start_time, formData.end_time)
    }
  }

  /**
   * 显示表单（供FormManager调用）
   */
  const showForm = async (id?: number | string) => {
    console.log('hr_outing-form showForm called with id:', id)

    if (id && id !== '0') {
      await loadFormData(id)
    } else {
      // 重置表单为发起状态
      resetForm()
    }
  }

  /**
   * 重置表单
   */
  const resetForm = () => {
    Object.assign(formData, {
      id: undefined,
      start_time: '',
      end_time: '',
      duration: 0,
      purpose: '',
      attachment: [],
      approval_status: 0,
      workflow_instance_id: 0
    })
  }

  /**
   * 加载表单数据
   */
  const loadFormData = async (id: number | string) => {
    try {
      loading.value = true
      // 使用通用工作流接口获取详情
      const response = await ApplicationApi.detail(id)

      if (response.data) {
        // 合并表单数据
        Object.assign(formData, response.data.formData || {})

        // 设置ID和状态
        formData.id = response.data.id
        formData.approval_status = response.data.approval_status
        formData.approval_status_text = response.data.approval_status_text
        formData.workflow_instance_id = response.data.workflow_instance_id

        console.log('外出申请表单数据加载完成:', formData)
      }
    } catch (error) {
      console.error('加载表单数据失败:', error)
    } finally {
      loading.value = false
    }
  }

  /**
   * 设置表单数据（供FormManager调用）
   */
  const setFormData = (data: any) => {
    console.log('hr_outing-form setFormData called with:', data)
    Object.assign(formData, data)
  }

  /**
   * 保存表单数据
   */
  const handleSave = async () => {
    console.log('hr_outing-form.handleSave 被调用')
    try {
      // 表单验证
      const valid = await formRef.value?.validate()
      if (!valid) return

      saving.value = true

      // 准备业务数据
      const businessData: HrOutingFormData = {
        start_time: formData.start_time,
        end_time: formData.end_time,
        duration: formData.duration,
        purpose: formData.purpose,
        attachment: formData.attachment
      }

      // 如果是编辑模式，添加ID
      if (formData.id) {
        businessData.id = formData.id
      }

      console.log('外出申请保存数据:', businessData)
      emit('save', businessData)
    } catch (error) {
      console.error('保存失败:', error)
    } finally {
      saving.value = false
    }
  }

  /**
   * 提交审批
   */
  const handleSubmit = async () => {
    try {
      // 表单验证
      const valid = await formRef.value?.validate()
      if (!valid) return

      await ElMessageBox.confirm('确定要提交审批吗？提交后将无法修改。', '确认提交', {
        confirmButtonText: '确定提交',
        cancelButtonText: '取消',
        type: 'warning'
      })

      submitting.value = true

      // 准备业务数据
      const businessData: HrOutingFormData = {
        start_time: formData.start_time,
        end_time: formData.end_time,
        duration: formData.duration,
        purpose: formData.purpose,
        attachment: formData.attachment
      }

      // 如果是编辑模式，添加ID
      if (formData.id) {
        businessData.id = formData.id
      }

      console.log('外出申请提交数据:', businessData)
      emit('submit', businessData)
    } catch (error) {
      if (error !== 'cancel') {
        console.error('提交失败:', error)
      }
    } finally {
      submitting.value = false
    }
  }

  /**
   * 取消操作
   */
  const handleCancel = () => {
    emit('cancel')
    dialogVisible.value = false
  }

  // 监听时间变化自动计算时长
  watch([() => formData.start_time, () => formData.end_time], () => {
    calculateDuration()
  })

  // 暴露方法供父组件调用
  defineExpose({
    showForm,
    setFormData,
    formRef,
    formData,
    saving,
    submitting
  })
</script>

<style scoped lang="scss">
  .dialog-footer {
    text-align: right;
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
  }
</style>
