<template>
  <ul class="card-list">
    <li class="art-custom-card" v-for="(item, index) in cardData" :key="index">
      <span class="des subtitle">{{ item.des }}</span>
      <CountTo 
        class="number box-title" 
        :endVal="item.num" 
        :duration="1000" 
        separator=""
        :decimals="item.decimals || 0"
      ></CountTo>
      <div class="change-box">
        <span class="change-text">{{ item.unit }}</span>
        <span class="change text-primary">{{ item.percentage }}</span>
      </div>
      <i class="iconfont-sys" v-html="item.icon"></i>
    </li>
  </ul>
</template>

<script setup lang="ts">
  import { computed } from 'vue'
  import { CountTo } from 'vue3-count-to'

  interface Props {
    statsData?: any
    loading?: boolean
  }

  const props = withDefaults(defineProps<Props>(), {
    statsData: () => ({}),
    loading: false
  })

  const cardData = computed(() => {
    const data = props.statsData
    const totalHours = (data.leave_hours || 0) + (data.outing_hours || 0) + (data.business_trip_hours || 0)
    
    return [
      {
        des: '请假时长',
        icon: '&#xe721;',
        num: data.leave_hours || 0,
        decimals: 1,
        unit: '小时',
        percentage: totalHours > 0 ? `${((data.leave_hours || 0) / totalHours * 100).toFixed(1)}%` : '0%'
      },
      {
        des: '外出时长',
        icon: '&#xe724;',
        num: data.outing_hours || 0,
        decimals: 1,
        unit: '小时',
        percentage: totalHours > 0 ? `${((data.outing_hours || 0) / totalHours * 100).toFixed(1)}%` : '0%'
      },
      {
        des: '出差时长',
        icon: '&#xe7aa;',
        num: data.business_trip_hours || 0,
        decimals: 1,
        unit: '小时',
        percentage: totalHours > 0 ? `${((data.business_trip_hours || 0) / totalHours * 100).toFixed(1)}%` : '0%'
      },
      {
        des: '总计时长',
        icon: '&#xe82a;',
        num: totalHours,
        decimals: 1,
        unit: '小时',
        percentage: data.display_format || '0小时'
      }
    ]
  })
</script>

<style lang="scss" scoped>
  .card-list {
    box-sizing: border-box;
    display: flex;
    flex-wrap: wrap;
    width: calc(100% + var(--console-margin));
    margin-top: 0 !important;
    margin-left: calc(0px - var(--console-margin));
    background-color: transparent !important;

    li {
      position: relative;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      justify-content: center;
      width: calc(25% - var(--console-margin));
      height: 140px;
      padding: 0 18px;
      margin: 0 0 0 var(--console-margin);
      background: var(--art-main-bg-color);

      $icon-size: 52px;

      .iconfont-sys {
        position: absolute;
        top: 0;
        right: 20px;
        bottom: 0;
        width: $icon-size;
        height: $icon-size;
        margin: auto;
        overflow: hidden;
        font-size: 22px;
        line-height: $icon-size;
        color: var(--el-color-primary) !important;
        text-align: center;
        background-color: var(--el-color-primary-light-9);
        border-radius: 12px;
      }

      .des {
        display: block;
        height: 14px;
        font-size: 14px;
        line-height: 14px;
      }

      .number {
        display: block;
        margin-top: 10px;
        font-size: 28px;
        font-weight: 400;
      }

      .change-box {
        display: flex;
        align-items: center;
        margin-top: 10px;

        .change-text {
          display: block;
          font-size: 13px;
          color: var(--art-text-gray-600);
        }

        .change {
          display: block;
          margin-left: 5px;
          font-size: 13px;
          font-weight: bold;
        }
      }

      // 最后一个卡片特殊样式
      &:last-child {
        background: linear-gradient(135deg, var(--el-color-primary) 0%, var(--el-color-primary-dark-2) 100%);
        color: white;

        .des, .change-text, .change {
          color: rgba(255, 255, 255, 0.9) !important;
        }

        .number {
          color: white !important;
        }

        .iconfont-sys {
          background-color: rgba(255, 255, 255, 0.2) !important;
          color: white !important;
        }
      }
    }
  }

  .dark {
    .card-list {
      li {
        .iconfont-sys {
          background-color: #232323 !important;
        }

        &:last-child .iconfont-sys {
          background-color: rgba(255, 255, 255, 0.2) !important;
        }
      }
    }
  }
</style>
