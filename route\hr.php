<?php
/**
 * HR模块路由配置
 *
 * 包含请假申请等HR相关功能的路由定义
 */

use app\common\middleware\TokenAuthMiddleware;
use think\facade\Route;

// HR请假申请路由组
Route::group('api/hr', function () {
	
	// 请假申请相关路由
	Route::group('hr_leave', function () {
		
		// 基础CRUD路由
		//		Route::get('list', 'hr.HrLeave/list');                    // 获取请假申请列表
		//		Route::get('myList', 'hr.HrLeave/myList');                // 获取我的请假申请列表
		//		Route::get('detail/<id>', 'hr.HrLeave/detail');           // 获取请假申请详情
		//		Route::post('add', 'hr.HrLeave/add');                     // 新增请假申请
		//		Route::post('edit/<id>', 'hr.HrLeave/edit');               // 编辑请假申请
		//		Route::delete('delete/<id>', 'hr.HrLeave/delete');        // 删除请假申请
		//		Route::post('batchDelete', 'hr.HrLeave/batchDelete');     // 批量删除请假申请
		//
		//		// 工作流操作路由
		//		Route::post('submit/<id>', 'hr.HrLeave/submit');          // 提交审批
		//		Route::post('withdraw/<id>', 'hr.HrLeave/withdraw');      // 撤回审批
		//		Route::post('void/<id>', 'hr.HrLeave/void');              // 作废申请
		
		// 业务查询路由
		//		Route::get('statistics', 'hr.HrLeave/statistics');             // 获取统计数据
		Route::get('leaveTypes', 'app\hr\controller\HrLeaveController@leaveTypes');             // 获取请假类型选项
		//		Route::get('approvalStatuses', 'hr.HrLeave/approvalStatuses'); // 获取审批状态选项
		
	});
	
})
     ->middleware([
	     TokenAuthMiddleware::class,
	     //    PermissionMiddleware::class
     ]); // 添加认证和权限中间件

Route::group('api/hr/monthly_stats', function () {
	Route::get('employee', 'app\hr\controller\HrMonthlyStats@getEmployeeStats');
	Route::get('department', 'app\hr\controller\HrMonthlyStats@getDepartmentStats');
	Route::get('yearly', 'app\hr\controller\HrMonthlyStats@getEmployeeYearlyStats');
	Route::get('overview', 'app\hr\controller\HrMonthlyStats@getStatsOverview');
	Route::get('export/employee', 'app\hr\controller\HrMonthlyStats@exportEmployeeStats');
	Route::get('export/department', 'app\hr\controller\HrMonthlyStats@exportDepartmentStats');
	Route::get('config', 'app\hr\controller\HrMonthlyStats@getStatsConfig');
})
     ->middleware([TokenAuthMiddleware::class]);
