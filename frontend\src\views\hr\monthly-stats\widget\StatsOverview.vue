<template>
  <div class="region stats-overview art-custom-card">
    <div class="card-header">
      <div class="title">
        <h4 class="box-title">统计概览</h4>
        <p class="subtitle">当月汇总数据</p>
      </div>
    </div>
    <div class="overview-content" v-loading="loading">
      <div class="overview-item">
        <div class="item-label">统计范围</div>
        <div class="item-value">{{ overviewData.scope === 'all' ? '全员' : '个人' }}</div>
      </div>
      
      <div class="overview-item">
        <div class="item-label">统计月份</div>
        <div class="item-value">{{ overviewData.year_month || '--' }}</div>
      </div>
      
      <div class="overview-item">
        <div class="item-label">请假时长</div>
        <div class="item-value highlight-leave">{{ overviewData.leave_hours || 0 }}小时</div>
      </div>
      
      <div class="overview-item">
        <div class="item-label">外出时长</div>
        <div class="item-value highlight-outing">{{ overviewData.outing_hours || 0 }}小时</div>
      </div>
      
      <div class="overview-item">
        <div class="item-label">出差时长</div>
        <div class="item-value highlight-trip">{{ overviewData.business_trip_hours || 0 }}小时</div>
      </div>
      
      <div class="overview-item total">
        <div class="item-label">总计时长</div>
        <div class="item-value">{{ overviewData.display_format || '0小时' }}</div>
      </div>
      
      <div class="overview-item">
        <div class="item-label">工作时间</div>
        <div class="item-value">{{ overviewData.daily_work_hours || 8 }}小时/天</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  interface Props {
    overviewData?: any
    loading?: boolean
  }

  const props = withDefaults(defineProps<Props>(), {
    overviewData: () => ({}),
    loading: false
  })
</script>

<style lang="scss" scoped>
  .stats-overview {
    height: 400px;
    padding: 20px;

    .overview-content {
      height: calc(100% - 60px);
      display: flex;
      flex-direction: column;
      gap: 16px;
      padding-top: 10px;
    }

    .overview-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      background: var(--art-bg-color);
      border-radius: 8px;
      transition: all 0.3s;

      &:hover {
        background: var(--el-color-primary-light-9);
        transform: translateX(2px);
      }

      &.total {
        background: linear-gradient(135deg, var(--el-color-primary-light-7) 0%, var(--el-color-primary-light-8) 100%);
        border: 1px solid var(--el-color-primary-light-5);
        
        .item-label {
          font-weight: 600;
          color: var(--el-color-primary);
        }
        
        .item-value {
          font-weight: 600;
          color: var(--el-color-primary);
          font-size: 16px;
        }
      }

      .item-label {
        font-size: 14px;
        color: var(--art-text-gray-600);
        font-weight: 500;
      }

      .item-value {
        font-size: 14px;
        color: var(--art-text-gray-800);
        font-weight: 600;

        &.highlight-leave {
          color: #5470c6;
        }

        &.highlight-outing {
          color: #91cc75;
        }

        &.highlight-trip {
          color: #fac858;
        }
      }
    }
  }

  .dark {
    .stats-overview {
      .overview-item {
        background: var(--art-dark-bg-color);

        &:hover {
          background: rgba(255, 255, 255, 0.05);
        }

        &.total {
          background: rgba(var(--el-color-primary-rgb), 0.1);
          border-color: rgba(var(--el-color-primary-rgb), 0.3);
        }

        .item-label {
          color: var(--art-text-gray-400);
        }

        .item-value {
          color: var(--art-text-gray-200);
        }
      }
    }
  }

  @media screen and (max-width: $device-ipad-vertical) {
    .stats-overview {
      height: 300px;
      
      .overview-content {
        gap: 12px;
      }
      
      .overview-item {
        padding: 10px 14px;
        
        .item-label, .item-value {
          font-size: 13px;
        }
      }
    }
  }
</style>
