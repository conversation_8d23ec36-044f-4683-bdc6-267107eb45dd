<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="39f9af6e-210a-482e-b41a-b0ffb2d6544c" name="更改" comment="">
      <change afterPath="$PROJECT_DIR$/src/api/workflow/ApplicationApi.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/api/workflow/WorkflowTaskApi.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/components/core/tables/表格列整合使用.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/views/crm/crm_customer_sea/form-dialog.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/views/crm/crm_customer_sea/list.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/views/crm/crm_follow_record/form-dialog.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/views/crm/crm_follow_record/list.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/views/crm/crm_lead_pool/list.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/views/notice/Message.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/views/workflow/Application.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/views/workflow/WorkflowTask.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/views/workflow/components/business-forms/hr_leave-form.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/views/workflow/components/form-manager.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/views/workflow/components/workflow-type-selector.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.auto-import.json" beforeDir="false" afterPath="$PROJECT_DIR$/.auto-import.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.cursor/README.md" beforeDir="false" afterPath="$PROJECT_DIR$/.cursor/README.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.cursor/rules.json" beforeDir="false" afterPath="$PROJECT_DIR$/.cursor/rules.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/README.md" beforeDir="false" afterPath="$PROJECT_DIR$/README.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/README.zh-CN.md" beforeDir="false" afterPath="$PROJECT_DIR$/README.zh-CN.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/attendance-leave-form.json" beforeDir="false" afterPath="$PROJECT_DIR$/attendance-leave-form.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/base.sql" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/components.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/components.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/index.html" beforeDir="false" afterPath="$PROJECT_DIR$/index.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/pnpm-lock.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/pnpm-lock.yaml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/App.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/App.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/api/adminApi.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/api/adminApi.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/api/departmentApi.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/api/departmentApi.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/api/menuApi.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/api/menuApi.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/api/workflow/WorkflowTypeApi.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/api/workflow/workflowDefinitionApi.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/api/workflow/workflowFormApi.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/api/workflow/workflowInstanceApi.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/assets/icons/system/iconfont.css" beforeDir="false" afterPath="$PROJECT_DIR$/src/assets/icons/system/iconfont.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/assets/icons/system/iconfont.js" beforeDir="false" afterPath="$PROJECT_DIR$/src/assets/icons/system/iconfont.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/assets/icons/system/iconfont.json" beforeDir="false" afterPath="$PROJECT_DIR$/src/assets/icons/system/iconfont.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/assets/styles/app.scss" beforeDir="false" afterPath="$PROJECT_DIR$/src/assets/styles/app.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/assets/styles/change.scss" beforeDir="false" afterPath="$PROJECT_DIR$/src/assets/styles/change.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/assets/styles/dark.scss" beforeDir="false" afterPath="$PROJECT_DIR$/src/assets/styles/dark.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/assets/styles/el-dark.scss" beforeDir="false" afterPath="$PROJECT_DIR$/src/assets/styles/el-dark.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/assets/styles/el-light.scss" beforeDir="false" afterPath="$PROJECT_DIR$/src/assets/styles/el-light.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/assets/styles/el-ui.scss" beforeDir="false" afterPath="$PROJECT_DIR$/src/assets/styles/el-ui.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/assets/styles/markdown.scss" beforeDir="false" afterPath="$PROJECT_DIR$/src/assets/styles/markdown.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/assets/styles/mixin.scss" beforeDir="false" afterPath="$PROJECT_DIR$/src/assets/styles/mixin.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/assets/styles/mobile.scss" beforeDir="false" afterPath="$PROJECT_DIR$/src/assets/styles/mobile.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/assets/styles/one-dark-pro.scss" beforeDir="false" afterPath="$PROJECT_DIR$/src/assets/styles/one-dark-pro.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/assets/styles/reset.scss" beforeDir="false" afterPath="$PROJECT_DIR$/src/assets/styles/reset.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/assets/styles/theme-animation.scss" beforeDir="false" afterPath="$PROJECT_DIR$/src/assets/styles/theme-animation.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/assets/styles/transition.scss" beforeDir="false" afterPath="$PROJECT_DIR$/src/assets/styles/transition.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/assets/styles/tree.scss" beforeDir="false" afterPath="$PROJECT_DIR$/src/assets/styles/tree.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/assets/styles/variables.scss" beforeDir="false" afterPath="$PROJECT_DIR$/src/assets/styles/variables.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/assets/workflow/css/dialog.css" beforeDir="false" afterPath="$PROJECT_DIR$/src/assets/workflow/css/dialog.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/assets/workflow/css/workflow.css" beforeDir="false" afterPath="$PROJECT_DIR$/src/assets/workflow/css/workflow.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/core/base/ArtIconSelector.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/core/base/ArtIconSelector.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/core/forms/ArtButtonTable.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/core/forms/ArtButtonTable.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/core/forms/ArtWangEditor.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/core/forms/ArtWangEditor.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/core/forms/art-search-bar/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/core/forms/art-search-bar/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/core/forms/art-search-bar/widget/art-search-date/README.md" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/core/forms/art-search-bar/widget/art-search-date/README.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/core/forms/art-search-bar/widget/art-search-input/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/core/forms/art-search-bar/widget/art-search-input/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/core/layouts/art-fireworks-effect/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/core/layouts/art-fireworks-effect/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/core/layouts/art-header-bar/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/core/layouts/art-header-bar/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/core/layouts/art-header-bar/mobile.scss" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/core/layouts/art-header-bar/mobile.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/core/layouts/art-header-bar/style.scss" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/core/layouts/art-header-bar/style.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/core/layouts/art-menus/art-sidebar-menu/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/core/layouts/art-menus/art-sidebar-menu/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/core/layouts/art-menus/art-sidebar-menu/style.scss" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/core/layouts/art-menus/art-sidebar-menu/style.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/core/layouts/art-menus/art-sidebar-menu/theme.scss" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/core/layouts/art-menus/art-sidebar-menu/theme.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/core/layouts/art-settings-panel/composables/useSettingsConfig.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/core/layouts/art-settings-panel/composables/useSettingsConfig.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/core/layouts/art-settings-panel/composables/useSettingsHandlers.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/core/layouts/art-settings-panel/composables/useSettingsHandlers.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/core/layouts/art-settings-panel/composables/useSettingsPanel.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/core/layouts/art-settings-panel/composables/useSettingsPanel.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/core/layouts/art-settings-panel/composables/useSettingsState.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/core/layouts/art-settings-panel/composables/useSettingsState.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/core/layouts/art-settings-panel/widget/BoxStyleSettings.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/core/layouts/art-settings-panel/widget/BoxStyleSettings.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/core/layouts/art-settings-panel/widget/ColorSettings.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/core/layouts/art-settings-panel/widget/ColorSettings.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/core/layouts/art-settings-panel/widget/ContainerSettings.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/core/layouts/art-settings-panel/widget/ContainerSettings.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/core/layouts/art-settings-panel/widget/MenuLayoutSettings.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/core/layouts/art-settings-panel/widget/MenuLayoutSettings.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/core/layouts/art-settings-panel/widget/MenuStyleSettings.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/core/layouts/art-settings-panel/widget/MenuStyleSettings.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/core/layouts/art-settings-panel/widget/SectionTitle.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/core/layouts/art-settings-panel/widget/SectionTitle.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/core/layouts/art-settings-panel/widget/SettingDrawer.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/core/layouts/art-settings-panel/widget/SettingDrawer.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/core/layouts/art-settings-panel/widget/SettingHeader.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/core/layouts/art-settings-panel/widget/SettingHeader.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/core/layouts/art-settings-panel/widget/SettingItem.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/core/layouts/art-settings-panel/widget/SettingItem.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/core/layouts/art-settings-panel/widget/ThemeSettings.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/core/layouts/art-settings-panel/widget/ThemeSettings.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/core/layouts/art-work-tab/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/core/layouts/art-work-tab/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/core/layouts/art-work-tab/style.scss" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/core/layouts/art-work-tab/style.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/core/tables/ArtTable.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/core/tables/ArtTable.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/core/tables/ArtTableFullScreen.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/core/tables/ArtTableFullScreen.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/core/tables/ArtTableHeader.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/core/tables/ArtTableHeader.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/DepartmentTreeSelect.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/custom/DepartmentTreeSelect.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/FormMediaSelector/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/custom/FormMediaSelector/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/ImageViewer/index.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/custom/ImageViewer/index.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/ImageViewer/src/ImageViewer.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/custom/ImageViewer/src/ImageViewer.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/ImageViewer/src/types.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/custom/ImageViewer/src/types.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/MediaSelector/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/custom/MediaSelector/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/PostSelect.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/custom/PostSelect.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/SimpleProcessDesignerV2/src/NodeHandler.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/SimpleProcessDesignerV2/src/ProcessNodeTree.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/SimpleProcessDesignerV2/src/SimpleProcessDesigner.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/SimpleProcessDesignerV2/src/SimpleProcessModel.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/SimpleProcessDesignerV2/src/SimpleProcessViewer.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/SimpleProcessDesignerV2/src/consts.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/SimpleProcessDesignerV2/src/index.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/SimpleProcessDesignerV2/src/node.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/SimpleProcessDesignerV2/src/nodes-config/ChildProcessNodeConfig.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/SimpleProcessDesignerV2/src/nodes-config/ConditionNodeConfig.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/SimpleProcessDesignerV2/src/nodes-config/CopyTaskNodeConfig.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/SimpleProcessDesignerV2/src/nodes-config/DelayTimerNodeConfig.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/SimpleProcessDesignerV2/src/nodes-config/RouterNodeConfig.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/SimpleProcessDesignerV2/src/nodes-config/StartUserNodeConfig.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/SimpleProcessDesignerV2/src/nodes-config/TriggerNodeConfig.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/SimpleProcessDesignerV2/src/nodes-config/UserTaskNodeConfig.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/SimpleProcessDesignerV2/src/nodes-config/components/Condition.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/SimpleProcessDesignerV2/src/nodes-config/components/ConditionDialog.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/SimpleProcessDesignerV2/src/nodes-config/components/HttpRequestParamSetting.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/SimpleProcessDesignerV2/src/nodes-config/components/HttpRequestSetting.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/SimpleProcessDesignerV2/src/nodes-config/components/UserTaskListener.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/SimpleProcessDesignerV2/src/nodes/ChildProcessNode.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/SimpleProcessDesignerV2/src/nodes/CopyTaskNode.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/SimpleProcessDesignerV2/src/nodes/DelayTimerNode.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/SimpleProcessDesignerV2/src/nodes/EndEventNode.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/SimpleProcessDesignerV2/src/nodes/ExclusiveNode.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/SimpleProcessDesignerV2/src/nodes/InclusiveNode.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/SimpleProcessDesignerV2/src/nodes/ParallelNode.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/SimpleProcessDesignerV2/src/nodes/RouterNode.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/SimpleProcessDesignerV2/src/nodes/StartUserNode.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/SimpleProcessDesignerV2/src/nodes/TriggerNode.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/SimpleProcessDesignerV2/src/nodes/UserTaskNode.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/SimpleProcessDesignerV2/src/utils.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/SimpleProcessDesignerV2/theme/iconfont.ttf" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/SimpleProcessDesignerV2/theme/iconfont.woff" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/SimpleProcessDesignerV2/theme/iconfont.woff2" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/SimpleProcessDesignerV2/theme/simple-process-designer.scss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/UploadFile/index.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/UploadFile/src/UploadFile.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/UploadFile/src/UploadImg.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/UploadFile/src/UploadImgs.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/UploadFile/src/useUpload.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/comment-widget/widget/CommentItem.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/custom/comment-widget/widget/CommentItem.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/workflow/README.md" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/custom/workflow/README.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/workflow/api/mockData.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/custom/workflow/api/mockData.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/workflow/components/AddNode.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/custom/workflow/components/AddNode.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/workflow/components/NodeWrap.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/custom/workflow/components/NodeWrap.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/workflow/components/dialogs/ErrorDialog.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/custom/workflow/components/dialogs/ErrorDialog.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/workflow/components/drawers/ApproverDrawer.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/custom/workflow/components/drawers/ApproverDrawer.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/workflow/components/drawers/ConditionDrawer.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/custom/workflow/components/drawers/ConditionDrawer.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/workflow/components/drawers/CopyerDrawer.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/custom/workflow/components/drawers/CopyerDrawer.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/workflow/components/drawers/PromoterDrawer.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/custom/workflow/components/drawers/PromoterDrawer.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/workflow/components/selectors/EmployeeSelector.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/custom/workflow/components/selectors/EmployeeSelector.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/workflow/components/selectors/UserSelector.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/custom/workflow/components/selectors/UserSelector.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/workflow/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/custom/workflow/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/custom/workflow/styles/workflow.scss" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/custom/workflow/styles/workflow.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/composables/useAuth.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/composables/useAuth.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/directives/focus.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/directives/focus.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/directives/index.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/directives/index.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/directives/permission.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/directives/permission.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/language/index.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/language/index.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/main.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/router/guards/beforeEach.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/router/guards/beforeEach.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/router/menu-handler.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/router/menu-handler.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/router/routes/staticRoutes.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/router/routes/staticRoutes.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/router/routesAlias.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/router/routesAlias.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/router/utils/registerRoutes.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/router/utils/registerRoutes.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/store/modules/config.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/store/modules/config.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/store/modules/setting.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/store/modules/setting.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/store/modules/upload.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/store/modules/upload.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/store/modules/user.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/store/modules/user.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/store/modules/workflow.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/store/modules/workflow.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/types/auto-imports.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/types/auto-imports.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/types/components.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/types/components.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/types/search-form.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/types/search-form.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/utils/http/index.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/utils/http/index.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/utils/jump.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/utils/jump.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/utils/loading.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/utils/loading.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/utils/storage/storage-config.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/utils/storage/storage-config.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/utils/storage/storage-key-manager.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/utils/storage/storage-key-manager.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/utils/upload/AliyunUploader.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/utils/upload/AliyunUploader.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/utils/upload/IUploader.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/utils/upload/IUploader.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/utils/upload/QiniuUploader.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/utils/upload/QiniuUploader.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/utils/upload/TencentUploader.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/utils/upload/TencentUploader.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/utils/upload/UploadService.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/utils/upload/UploadService.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/utils/utils.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/utils/utils.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/dashboard/analysis/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/dashboard/analysis/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/dashboard/analysis/widget/CustomerSatisfaction.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/dashboard/analysis/widget/CustomerSatisfaction.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/dashboard/analysis/widget/SalesMappingByCountry.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/dashboard/analysis/widget/SalesMappingByCountry.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/dashboard/analysis/widget/TargetVsReality.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/dashboard/analysis/widget/TargetVsReality.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/dashboard/analysis/widget/TodaySales.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/dashboard/analysis/widget/TodaySales.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/dashboard/analysis/widget/TopProducts.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/dashboard/analysis/widget/TopProducts.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/dashboard/analysis/widget/TotalRevenue.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/dashboard/analysis/widget/TotalRevenue.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/dashboard/analysis/widget/VisitorInsights.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/dashboard/analysis/widget/VisitorInsights.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/dashboard/analysis/widget/VolumeServiceLevel.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/dashboard/analysis/widget/VolumeServiceLevel.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/dashboard/console/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/dashboard/console/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/dashboard/console/widget/ActiveUser.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/dashboard/console/widget/ActiveUser.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/dashboard/console/widget/CardList.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/dashboard/console/widget/CardList.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/dashboard/console/widget/Dynamic.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/dashboard/console/widget/Dynamic.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/dashboard/console/widget/TodoList.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/dashboard/console/widget/TodoList.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/dashboard/ecommerce/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/dashboard/ecommerce/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/dashboard/ecommerce/widget/AnnualSales.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/dashboard/ecommerce/widget/AnnualSales.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/dashboard/ecommerce/widget/Banner.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/dashboard/ecommerce/widget/Banner.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/dashboard/ecommerce/widget/CartConversionRate.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/dashboard/ecommerce/widget/CartConversionRate.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/dashboard/ecommerce/widget/HotCommodity.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/dashboard/ecommerce/widget/HotCommodity.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/dashboard/ecommerce/widget/HotProductsList.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/dashboard/ecommerce/widget/HotProductsList.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/dashboard/ecommerce/widget/ProductSales.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/dashboard/ecommerce/widget/ProductSales.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/dashboard/ecommerce/widget/RecentTransaction.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/dashboard/ecommerce/widget/RecentTransaction.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/dashboard/ecommerce/widget/SalesClassification.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/dashboard/ecommerce/widget/SalesClassification.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/dashboard/ecommerce/widget/SalesGrowth.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/dashboard/ecommerce/widget/SalesGrowth.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/dashboard/ecommerce/widget/SalesTrend.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/dashboard/ecommerce/widget/SalesTrend.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/dashboard/ecommerce/widget/TotalOrderVolume.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/dashboard/ecommerce/widget/TotalOrderVolume.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/dashboard/ecommerce/widget/TotalProducts.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/dashboard/ecommerce/widget/TotalProducts.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/dashboard/ecommerce/widget/TransactionList.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/dashboard/ecommerce/widget/TransactionList.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/exception/403.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/exception/403.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/exception/404.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/exception/404.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/exception/500.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/exception/500.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/index/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/index/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/index/style.scss" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/index/style.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/login/index.scss" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/login/index.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/login/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/login/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/outside/Iframe.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/outside/Iframe.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/permission/Admin.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/permission/Admin.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/permission/Dept.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/permission/Dept.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/permission/Menu.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/permission/Menu.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/permission/Post.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/permission/Post.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/permission/Role.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/permission/Role.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/result/Fail.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/result/Fail.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/result/Success.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/result/Success.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/system/Config.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/system/Config.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/system/UserCenter.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/system/UserCenter.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/system/attachment/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/system/attachment/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/system/attachment/index1.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/system/attachment/index1.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/template/Banners.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/template/Banners.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/template/Calendar.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/template/Calendar.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/template/Cards.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/template/Cards.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/template/Charts.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/template/Charts.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/template/Chat.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/template/Chat.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/template/Map.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/template/Map.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/template/Pricing.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/template/Pricing.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/widgets/CountTo.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/widgets/CountTo.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/widgets/Drag.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/widgets/Drag.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/widgets/Excel.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/widgets/Excel.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/widgets/Fireworks.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/widgets/Fireworks.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/widgets/IconList.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/widgets/IconList.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/widgets/ImageCrop.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/widgets/ImageCrop.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/widgets/Qrcode.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/widgets/Qrcode.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/widgets/TextScroll.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/widgets/TextScroll.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/widgets/Video.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/widgets/Video.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/widgets/WangEditor.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/widgets/WangEditor.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/widgets/Watermark.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/widgets/Watermark.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/workflow/definition.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/workflow/definition_beifen.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/workflow/flow_type.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/workflow/form.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/notice/Template.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/workflow/list/index.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/workflow/test.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/tsconfig.json" beforeDir="false" afterPath="$PROJECT_DIR$/tsconfig.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/vite.config.ts" beforeDir="false" afterPath="$PROJECT_DIR$/vite.config.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/workflow-integration-guide.md" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="TypeScript File" />
        <option value="Vue Composition API Component" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="2yO6aq7bp489s9KeyJDWZvFfIOY" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;main&quot;,
    &quot;last_opened_file_path&quot;: &quot;E:/我的项目/强哥汉服/前端&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.stylelint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.stylelint&quot;: &quot;E:\\项目\\self_admin\\base_admin\\frontend\\art-design-pro\\node_modules\\stylelint&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;pnpm&quot;,
    &quot;prettierjs.PrettierConfiguration.Package&quot;: &quot;E:\\项目\\self_admin\\base_admin\\frontend\\node_modules\\prettier&quot;,
    &quot;ts.external.directory.path&quot;: &quot;E:\\项目\\self_admin\\base_admin\\frontend\\node_modules\\typescript\\lib&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;vue.recent.templates&quot;: [
      &quot;Vue Composition API Component&quot;
    ]
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\项目\self_admin\base_admin\frontend\src\api\crm" />
      <recent name="E:\项目\self_admin\base_admin\frontend\src\views\crm\crm_customer_sea" />
      <recent name="E:\项目\self_admin\base_admin\frontend\src\views\crm" />
      <recent name="E:\项目\self_admin\base_admin\frontend\src\api" />
      <recent name="E:\项目\self_admin\base_admin\frontend\src\views\workflow" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="E:\项目\self_admin\base_admin\frontend\art-design-pro\src\api\workflow" />
      <recent name="E:\项目\self_admin\base_admin\frontend\art-design-pro\src\views\workflow" />
      <recent name="E:\项目\self_admin\base_admin\frontend\art-design-pro\src\views\workflow\instance" />
    </key>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-1632447f56bf-JavaScript-WS-243.26053.12" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="39f9af6e-210a-482e-b41a-b0ffb2d6544c" name="更改" comment="" />
      <created>1749693338253</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1749693338253</updated>
      <workItem from="1749693339374" duration="5000000" />
      <workItem from="1749729260503" duration="8261000" />
      <workItem from="1749783844222" duration="12636000" />
      <workItem from="1749871090944" duration="9012000" />
      <workItem from="1749939299307" duration="21731000" />
      <workItem from="1750047155142" duration="3275000" />
      <workItem from="1750130830638" duration="10623000" />
      <workItem from="1750228404544" duration="1435000" />
      <workItem from="1750230152181" duration="14825000" />
      <workItem from="1750296789674" duration="4977000" />
      <workItem from="1750320723958" duration="3778000" />
      <workItem from="1750381790547" duration="6324000" />
      <workItem from="1750406869399" duration="10126000" />
      <workItem from="1750475491415" duration="19804000" />
      <workItem from="1750560611721" duration="12487000" />
      <workItem from="1750642413718" duration="6987000" />
      <workItem from="1750684964827" duration="1485000" />
      <workItem from="1750728113237" duration="16889000" />
      <workItem from="1750810221590" duration="7303000" />
      <workItem from="1750904897326" duration="9499000" />
      <workItem from="1750988732592" duration="6012000" />
      <workItem from="1751076438788" duration="9355000" />
      <workItem from="1751125971817" duration="436000" />
      <workItem from="1751161691523" duration="17264000" />
      <workItem from="1751213851306" duration="84000" />
      <workItem from="1751248177315" duration="26987000" />
      <workItem from="1751338243714" duration="2000" />
      <workItem from="1751419829916" duration="18972000" />
      <workItem from="1751505253754" duration="8760000" />
      <workItem from="1751595359187" duration="11080000" />
      <workItem from="1751680603341" duration="21373000" />
      <workItem from="1751724655886" duration="662000" />
      <workItem from="1751764204863" duration="12358000" />
      <workItem from="1751855061317" duration="6229000" />
      <workItem from="1751872364672" duration="3531000" />
      <workItem from="1751877823605" duration="16453000" />
      <workItem from="1751946698881" duration="15438000" />
      <workItem from="1751975340842" duration="1808000" />
      <workItem from="1752020129058" duration="4304000" />
      <workItem from="1752025290382" duration="11225000" />
      <workItem from="1752052647419" duration="5086000" />
      <workItem from="1752110801078" duration="70000" />
      <workItem from="1752135582602" duration="5276000" />
      <workItem from="1752214784103" duration="486000" />
      <workItem from="1752215281990" duration="10628000" />
      <workItem from="1752281432442" duration="17185000" />
      <workItem from="1752371388415" duration="20306000" />
      <workItem from="1752421857714" duration="2229000" />
      <workItem from="1752461002793" duration="28550000" />
      <workItem from="1752511421385" duration="1922000" />
      <workItem from="1752545974463" duration="26897000" />
      <workItem from="1752638079471" duration="18507000" />
      <workItem from="1752724537144" duration="18447000" />
      <workItem from="1752807772037" duration="14180000" />
      <workItem from="1752893546060" duration="30761000" />
      <workItem from="1752946757525" duration="3930000" />
      <workItem from="1752989869101" duration="7721000" />
      <workItem from="1753028553684" duration="3554000" />
      <workItem from="1753067260338" duration="16562000" />
      <workItem from="1753152906924" duration="18460000" />
      <workItem from="1753250411465" duration="3001000" />
      <workItem from="1753326766005" duration="18999000" />
      <workItem from="1753378207485" duration="800000" />
      <workItem from="1753419034051" duration="14791000" />
      <workItem from="1753496926964" duration="8910000" />
      <workItem from="1753573469181" duration="20888000" />
      <workItem from="1753679938969" duration="18526000" />
      <workItem from="1753760047795" duration="29173000" />
      <workItem from="1753810511283" duration="191000" />
      <workItem from="1753843630479" duration="9741000" />
      <workItem from="1753935840433" duration="10789000" />
      <workItem from="1753983964263" duration="1571000" />
      <workItem from="1754021285425" duration="4482000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>